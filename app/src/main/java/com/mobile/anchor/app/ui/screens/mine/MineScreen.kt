package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.OnlineStatus
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.activities.AlbumManageActivity
import com.mobile.anchor.app.ui.activities.LevelActivity
import com.mobile.anchor.app.ui.activities.ProfileEditActivity
import com.mobile.anchor.app.ui.activities.RewardTaskActivity
import com.mobile.anchor.app.ui.activities.SettingActivity
import com.mobile.anchor.app.ui.activities.WalletActivity
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.CardView
import com.mobile.anchor.app.ui.components.CircleAvatar
import com.mobile.anchor.app.ui.components.MineMenuItem
import com.mobile.anchor.app.ui.components.OnlineStatusSelector
import com.mobile.anchor.app.ui.components.ReviewStatusBadge
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.viewmodels.UserViewModel

/**
 * 个人资料页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MineScreen(
    viewModel: UserViewModel = viewModel(),
) {
    
    val context = LocalContext.current
    val userData by viewModel.userData.observeAsState(DataStoreManager.getUserObject())
    val reviewInfo by viewModel.reviewInfo.observeAsState()
    val lifecycleOwner = LocalLifecycleOwner.current

    val onlineStatus by viewModel.onlineStatus

    // 状态选择器显示状态
    var showStatusSelector by remember { mutableStateOf(false) }

    LaunchedEffect(lifecycleOwner) {
        lifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
            viewModel.fetchUserInfo()
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Image(
            painter = painterResource(R.mipmap.bg_mine),
            contentDescription = "背景",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        AnchorScaffold(containerColor = Color.Transparent) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // 头像和基本信息
                ProfileHeader(
                    userData = userData, reviewInfo = reviewInfo, onEditProfile = {
                        context.jump(ProfileEditActivity::class.java)
                    })

                FeatureCard()

                MenuCard()
            }
        }
    }

    // 在线状态选择器
    if (showStatusSelector) {
        OnlineStatusSelector(currentStatus = onlineStatus, onStatusSelected = { status ->
            viewModel.updateOnlineStatus(status)
        }, onDismiss = { showStatusSelector = false })
    }
}

/**
 * 个人资料头部
 */
@Composable
private fun ProfileHeader(
    userData: UserBean?, reviewInfo: ReviewBean?, onEditProfile: () -> Unit
) {
    Spacer(Modifier.height(60.dp))
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .clickable { onEditProfile() }) {
        val (avatar, nickname, age, country, id, language, arrow) = createRefs()

        // 头像
        Box(
            modifier = Modifier.constrainAs(avatar) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
            }) {
            CircleAvatar(imageUrl = userData?.avatar?.takeIf { it.isNotEmpty() }
                ?: "https://api.dicebear.com/7.x/micah/png?seed=test",
                size = 80.dp,
                contentDescription = "用户头像")

            // 在线状态指示器 - 显示在头像右上角
//            OnlineStatusIndicator(
//                statusValue = userData?.status,
//                modifier = Modifier
//                    .align(Alignment.TopEnd)
//                    .offset(x = (-5).dp, y = 5.dp),
//                size = 10
//            )

            // 头像审核状态覆盖层 - 显示在头像底部，与头像完美融合
            reviewInfo?.let { review ->
                if (!review.isCompletedAvatar) { // 只有非通过状态才显示
//                    ReviewStatusBadge(
//                        status = review.avatar_result,
//                        isAvatar = true,
//                        modifier = Modifier
//                            .align(Alignment.BottomCenter)
//                            .width(70.dp)
//                    )
                }
            }
        }

        // 昵称和审核状态
        Row(
            modifier = Modifier.constrainAs(nickname) {
                top.linkTo(avatar.top)
                start.linkTo(avatar.end, margin = 10.dp)
            }, verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = userData?.nickname ?: "",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )

            // 昵称审核状态 - 显示在昵称右边
            reviewInfo?.let { review ->
                if (!review.isCompleted) { // 只有非通过状态才显示
                    Spacer(modifier = Modifier.width(8.dp))
                    ReviewStatusBadge(
                        status = review.stat, isAvatar = false
                    )
                }
            }
        }

        Text(
            text = "${userData?.age ?: 0}",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xff73608c),
            modifier = Modifier
                .constrainAs(age) {
                    top.linkTo(nickname.bottom)
                    start.linkTo(nickname.start)
                }
                .border(1.dp, Color(0xff73608c), shape = MaterialTheme.shapes.small)
                .padding(horizontal = 5.dp, vertical = 1.dp))

        Text(
            text = userData?.country?.title ?: "",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xff73608c),
            modifier = Modifier
                .constrainAs(country) {
                    top.linkTo(age.top)
                    bottom.linkTo(age.bottom)
                    start.linkTo(age.end, margin = 5.dp)
                }
                .border(1.dp, Color(0xff73608c), shape = MaterialTheme.shapes.small)
                .padding(horizontal = 5.dp, vertical = 1.dp))

        Text(
            text = "ID: ${userData?.id}",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xff73608c),
            modifier = Modifier.constrainAs(id) {
                top.linkTo(age.bottom, margin = 5.dp)
                start.linkTo(age.start)
            })

        Row(
            modifier = Modifier.constrainAs(language) {
                top.linkTo(id.bottom, margin = 4.dp)
                start.linkTo(id.start)
            }, horizontalArrangement = Arrangement.spacedBy(5.dp)
        ) {
            Text(
                text = "${userData?.country?.title}",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xff8f94c7),
                modifier = Modifier

                    .background(color = Color(0XFF1B1D39), shape = MaterialTheme.shapes.small)
                    .padding(horizontal = 5.dp, vertical = 1.dp)
            )
//            Text(
//                text = "English",
//                style = MaterialTheme.typography.bodySmall,
//                color = Color(0xff8f94c7),
//                modifier = Modifier
//
//                    .background(color = Color(0XFF1B1D39), shape = MaterialTheme.shapes.small)
//                    .padding(horizontal = 5.dp, vertical = 1.dp)
//            )
//            Text(
//                text = "Japanese",
//                style = MaterialTheme.typography.bodySmall,
//                color = Color(0xff8f94c7),
//                modifier = Modifier
//                    .background(
//                        color = Color(0XFF1B1D39), shape = MaterialTheme.shapes.small
//                    )
//                    .padding(horizontal = 5.dp, vertical = 1.dp)
//            )
        }

        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = "进入",
            tint = Color.White,
            modifier = Modifier
                .constrainAs(arrow) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end, margin = 5.dp)
                }
                .clickable { onEditProfile() })
    }
}


@Composable
private fun FeatureCard() {
    val context = LocalContext.current
    Spacer(Modifier.height(30.dp))
    Row(modifier = Modifier.padding(horizontal = 16.dp)) {
        CardView(
            modifier = Modifier
                .weight(1f)
                .clickable {
                    context.jump(AlbumManageActivity::class.java)
                }) {
            Box {
                Row(modifier = Modifier.padding(horizontal = 10.dp, vertical = 10.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_mine_album),
                        contentDescription = "相册",
                        modifier = Modifier.size(54.dp)
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    Column {
                        Text(stringResource(R.string.album), color = Color.White, fontSize = 16.sp)
                        Spacer(modifier = Modifier.height(3.dp))
                        Text(
                            stringResource(R.string.public_private),
                            color = Color(0X99FFFFFF),
                            fontSize = 10.sp
                        )
                    }
                }
                Image(
                    painter = painterResource(R.mipmap.ic_mine_feature_label),
                    contentDescription = "Label",
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .size(50.dp)
                )
            }
        }
        Spacer(modifier = Modifier.width(10.dp))
        CardView(
            modifier = Modifier
                .weight(1f)
                .clickable {
                    context.jump(LevelActivity::class.java)
                }) {
            Box {
                Row(modifier = Modifier.padding(horizontal = 10.dp, vertical = 10.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_mine_level),
                        contentDescription = "Level",
                        modifier = Modifier.size(54.dp)
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    Column {
                        Text(stringResource(R.string.level), color = Color.White, fontSize = 16.sp)
                        Spacer(modifier = Modifier.height(3.dp))
                        Text(
                            stringResource(R.string.level_price),
                            color = Color(0X99FFFFFF),
                            fontSize = 10.sp
                        )
                    }
                }

                Image(
                    painter = painterResource(R.mipmap.ic_mine_feature_label),
                    contentDescription = "Label",
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .size(50.dp)
                )
            }
        }
    }
}

/**
 * 功能菜单
 */
@Composable
private fun MenuCard() {
    val context = LocalContext.current
    Spacer(Modifier.height(20.dp))
    CardView(modifier = Modifier.padding(horizontal = 16.dp)) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            MineMenuItem(
                title = stringResource(R.string.my_wallet),
                icon = R.mipmap.ic_mine_wallet,
                onClick = { context.jump(WalletActivity::class.java) })

            MineMenuItem(
                title = stringResource(R.string.reward_tasks),
                icon = R.mipmap.ic_mine_tasks,
                onClick = { context.jump(RewardTaskActivity::class.java) })

            MineMenuItem(
                title = stringResource(R.string.settings),
                icon = R.mipmap.ic_mine_setting,
                onClick = { context.jump(SettingActivity::class.java) })
        }
    }
}


@Preview
@Composable
fun MineScreenPreview() {
    AnchorTheme {
        Box {
            Image(
                painter = painterResource(R.mipmap.bg_mine),
                contentDescription = "背景",
                modifier = Modifier.fillMaxSize()
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // 头像和基本信息
                ProfileHeader(
                    userData = UserBean(
                        avatar = "https://api.dicebear.com/7.x/micah/png?seed=test",
                        nickname = "Nickname",
                        id = "123456",
                        fansCount = "100",
                        followCount = "99",
                        status = OnlineStatus.ONLINE.value
                    ), reviewInfo = null, onEditProfile = {})

                FeatureCard()

                MenuCard()
            }
        }
    }
}

@Preview
@Composable
fun MineScreenWithReviewStatusPreview() {
    AnchorTheme {
        Box {
            Image(
                painter = painterResource(R.mipmap.bg_mine),
                contentDescription = "背景",
                modifier = Modifier.fillMaxSize()
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // 头像和基本信息 - 带审核状态
                ProfileHeader(
                    userData = UserBean(
                        avatar = "https://api.dicebear.com/7.x/micah/png?seed=test",
                        nickname = "测试昵称",
                        id = "123456",
                        fansCount = "100",
                        followCount = "99",
                        status = OnlineStatus.BUSY.value // 显示忙碌状态（灰点）
                    ), reviewInfo = ReviewBean(
                        id = "1",
                        anchorId = "123456",
                        nickname = "测试昵称",
                        avatar = "",
                        gender = 1,
                        showAvatar = "",
                        videoFile = "",
                        showVideoFile = "",
                        reason = "",
                        birthday = 0L,
                        stat = 1,
                        updatedAt = 0L,
                        createdAt = 0L,
                        self_description = "",
                    ), onEditProfile = {})

                FeatureCard()

                MenuCard()
            }
        }
    }
}

@Preview
@Composable
fun MineScreenOnlineStatusPreview() {
    AnchorTheme {
        Box {
            Image(
                painter = painterResource(R.mipmap.bg_mine),
                contentDescription = "背景",
                modifier = Modifier.fillMaxSize()
            )

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(24.dp))

                // 头像和基本信息 - 在线状态
                ProfileHeader(
                    userData = UserBean(
                        avatar = "https://api.dicebear.com/7.x/micah/png?seed=test",
                        nickname = "在线用户",
                        id = "123456",
                        fansCount = "100",
                        followCount = "99",
                        status = OnlineStatus.ONLINE.value // 在线状态（绿点）
                    ), reviewInfo = null, onEditProfile = {})

                FeatureCard()

                MenuCard()
            }
        }
    }
}
