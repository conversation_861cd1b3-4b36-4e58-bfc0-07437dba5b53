package com.mobile.anchor.app

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.jumpThenFinish
import com.bdc.android.library.mvi.observeEvent
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.android.material.bottomnavigation.BottomNavigationMenuView
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.databinding.ActivityMainBinding
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.ui.activities.AlbumManageActivity
import com.mobile.anchor.app.ui.activities.BankBindActivity
import com.mobile.anchor.app.ui.activities.FaceRecordActivity
import com.mobile.anchor.app.ui.activities.LoginActivity
import com.mobile.anchor.app.ui.activities.ProfileBuildActivity
import com.mobile.anchor.app.ui.activities.ProfileEditActivity
import com.mobile.anchor.app.ui.conversation.MessageFragment
import com.mobile.anchor.app.ui.fragments.HomeFragment
import com.mobile.anchor.app.ui.fragments.MineFragment
import com.mobile.anchor.app.ui.fragments.RelationFragment
import com.mobile.anchor.app.ui.popup.ComposePopup
import com.mobile.anchor.app.ui.viewmodels.MainViewModel
import com.mobile.anchor.app.ui.viewmodels.MessagePageEvent
import com.mobile.anchor.app.ui.viewmodels.ReviewEvent
import com.mobile.anchor.app.ui.viewmodels.SocketMessageViewModel
import com.mobile.anchor.app.update.AppUpdateManager
import com.mobile.anchor.app.update.UpdateState
import com.mobile.anchor.app.utils.ActivityUtils
import com.mobile.anchor.app.utils.Constants
import io.rong.imkit.RongIM
import io.rong.imlib.RongIMClient
import io.rong.imlib.RongIMClient.ConnectCallback
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

class MainActivity : BaseCoreActivity<ActivityMainBinding, MainViewModel>() {

    private lateinit var binding: ActivityMainBinding
    private var currentFragment: Fragment? = null

    // Fragment实例缓存
    private var homeFragment = HomeFragment.newInstance()
    private var relationFragment = RelationFragment.newInstance()
    private var messageFragment = MessageFragment()
    private var mineFragment = MineFragment.newInstance()

    private var badgeView: View? = null
    private var badgeCountView: TextView? = null

    private val socketMessageViewModel: SocketMessageViewModel by viewModels()

    private var isDisplayExpiredDialog = false

    private lateinit var updateManager: AppUpdateManager
    private var isFirstResume = true

    val tabChangedFlow = MutableSharedFlow<Fragment?>(replay = 0)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge(
            SystemBarStyle.dark(Color.TRANSPARENT), SystemBarStyle.dark(Color.TRANSPARENT)
        )
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // 初始化工具类
        initializeUtils()
        ActivityUtils.addActivity(this)

        setupContent()

        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { view, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.navigationBars())

            // 1. 将顶部系统栏的高度设置为 fragment_container 的上边距
            //    这样 Fragment 的内容就不会与状态栏重叠
//            val containerParams = binding.fragmentContainer.layoutParams as ConstraintLayout.LayoutParams
//            containerParams.topMargin = insets.top
//            binding.fragmentContainer.layoutParams = containerParams

            // 2. 将底部导航栏的高度设置为 bottom_navigation 的下内边距
            //    这样导航栏本身会被“推”上来，而背景色仍然延伸到底部，视觉效果最好
//            binding.bottomNavigation.setPadding(0, 0, 0, insets.bottom)

            // 3. 清除根布局的任何边距，因为我们已经精确应用了 insets
            view.setPadding(0, 0, 0, insets.bottom)

            // 返回CONSUMED表示我们已经处理了这些insets
            WindowInsetsCompat.CONSUMED
        }

        mViewModel.getIsolatedConfig()

        FlowBus.with<String?>(Constants.TOKEN_EXPIRED).register(this, {
            LogX.d("令牌已过期，清除本地令牌")
            lifecycleScope.launch {
                DataStoreManager.clear()
            }
            if (!isDisplayExpiredDialog) {
                ComposePopup.showConfirmDialog(
                    this,
                    title = getString(R.string.reminder),
                    content = it?.takeIf { it.isNotEmpty() } ?: getString(R.string.login_expired),
                    showCancelButton = false,
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    onConfirm = {
                        jumpThenFinish(LoginActivity::class.java)
                        isDisplayExpiredDialog = false
                    })
                isDisplayExpiredDialog = true
            }
        })

        updateManager = AppUpdateManager.getInstance(this).apply {
            resetSessionState()
            enableAutoDialogs(this@MainActivity, isUserTriggered = true)
            checkUpdate(showNoUpdateToast = false, isUserTriggered = true)
        }

        lifecycleScope.launch {
            updateManager.updateState.collect {
                //暂无更新，或者用户拒绝更新
                if (it is UpdateState.NoUpdateAvailable || (it is UpdateState.Idle && updateManager.hasUserDeclinedThisSession())) {
                    mViewModel.checkUserReviewStatus()
                    handleReviewLogic()
                }
            }
        }
    }

    override fun isImmerse(): Boolean = true

    override fun getLayoutId(): Int {
        return R.layout.activity_main
    }

    override fun onResume() {
        super.onResume()
        if (isFirstResume) {
            isFirstResume = false
        } else {
            mViewModel.checkUserReviewStatus()
        }
        socketMessageViewModel.refreshUnreadMsg()
    }

    private fun handleReviewLogic() {
        mViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is ReviewEvent.FetchReviewSuccess -> {
                    //是否完善注册基本信息
                    if (it.reviewBean?.isMissing == true) {
                        jump(ProfileBuildActivity::class.java)
                        return@observeEvent
                    }
                    if (it?.userBean?.isVerified == false) {
                        handleRegistrationState(it.reviewBean)
                    } else {
                        LogX.d("开始绑定银行卡 ${it.userBean?.bank_id} ${DataStoreManager.getUserObject()?.bank_id}")
                        it.userBean?.bank_id?.let { it1 ->
                            if (it1 <= 0 && (DataStoreManager.getUserObject()?.bank_id ?: 0) <= 0) {
                                ComposePopup.showConfirmDialog(
                                    this@MainActivity,
                                    title = getString(R.string.reminder),
                                    content = getString(R.string.please_bind_your_bank_card_first),
                                    showCancelButton = false,
                                    dismissOnBackPress = false,
                                    dismissOnClickOutside = false,
                                    onConfirm = {
                                        jump(BankBindActivity::class.java)
                                    })
                            }
                        }
                    }
                }
            }
        }
    }

    private fun handleRegistrationState(reviewBean: ReviewBean?) {
        if (reviewBean == null) return
        if (reviewBean.isReviewing && reviewBean.check_reason.isNullOrEmpty()) {
            ComposePopup.showConfirmDialog(
                this,
                title = getString(R.string.reminder),
                content = getString(R.string.information_is_currently_under_review),
                showCancelButton = false,
                showConfirmButton = false,
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                autoDismiss = false
            )
            return
        }
        if (reviewBean.isReviewing || reviewBean.isRejected) {
            val pair: Pair<String, Class<out Activity>?> = when (reviewBean.check_jump_url) {
                "Album" -> {
                    Pair(reviewBean.check_reason, AlbumManageActivity::class.java)
                }

                "PersonalInformationUpdateFaile" -> {
                    Pair(reviewBean.check_reason, ProfileEditActivity::class.java)
                }

                "FaceVideo" -> {
                    Pair(reviewBean.check_reason, FaceRecordActivity::class.java)
                }

                "BankBind" -> {
                    Pair(reviewBean.check_reason, BankBindActivity::class.java)
                }

                else -> Pair(reviewBean.check_reason, null)
            }
            pair.second?.let {
                ComposePopup.showConfirmDialog(
                    this,
                    title = getString(R.string.reminder),
                    content = pair.first,
                    showCancelButton = false,
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    onConfirm = {
                        jump(it)
                    })
                return@let
            }
        }
    }

    /**
     * 显示主界面
     */
    private fun setupContent() {
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val rongYunToken = runBlocking {
            DataStoreManager.getString(DataStoreManager.KEY_RONG_YUN_TOKEN)
        }
        rongYunToken?.let {
            connectRongYunIM(it)
        }
        socketMessageViewModel.init()

        // 初始化 WebSocketManager
//        WebSocketManager.getInstance()
//            .init(applicationContext, "wss://api.idim888.com/api/v1/socket/ws")
        WebSocketManager.getInstance().init(
            applicationContext, BuildConfig.SOCKET_HOST
        )

        mViewModel.getVideoConfigDetail()

        // 重新初始化 Fragment，确保状态正确
        initializeFragments()

        // 设置底部导航
        setupBottomNavigation()

        // 默认显示首页
        showFragment(homeFragment)
    }

    private fun connectRongYunIM(token: String) {
        RongIM.connect(token, 0, object : ConnectCallback() {
            override fun onSuccess(userId: String) {
                LogX.e("RongYun connect success: $userId")
                socketMessageViewModel.refreshUnreadMsg()
            }

            override fun onError(e: RongIMClient.ConnectionErrorCode) {
                LogX.e("RongYun connect error: ${e.value}")
                //当提示token失效或者获取时重新连接 //TODO 后台会自动刷新token， 登录和获取用户详情时更新融云token
                when (e) {
//                    RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT, RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE -> mViewModel.getRongYunToken()
                    else -> {}
                }
            }

            override fun onDatabaseOpened(code: RongIMClient.DatabaseOpenStatus) {
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS == code) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                    LogX.e("RongYun connect onDatabaseOpened Failed: $code")
                }
            }
        })
    }

    private fun initializeUtils() {
        // 记录应用启动日志
        LogX.i("应用启动")

        // 检查是否首次启动
        if (DataStoreManager.isFirstLaunchSync()) {
            LogX.i("首次启动应用")
            // 使用协程设置首次启动标志
            MainScope().launch {
                DataStoreManager.setNotFirstLaunch()
            }
        }
    }

    /**
     * 初始化 Fragment
     */
    private fun initializeFragments() {
        try {
            // 检查FragmentManager状态
            if (supportFragmentManager.isStateSaved) {
                LogX.w("MainActivity: FragmentManager状态已保存，跳过Fragment初始化")
                return
            }

            // 清除之前的 Fragment，避免状态混乱
            supportFragmentManager.fragments.forEach { fragment ->
                if (fragment.isAdded) {
                    supportFragmentManager.beginTransaction().remove(fragment)
                        .commitAllowingStateLoss() // 使用commitAllowingStateLoss避免状态异常
                }
            }

            // 重新创建 Fragment 实例
            homeFragment = HomeFragment.newInstance()
            relationFragment = RelationFragment.newInstance()
            messageFragment = MessageFragment()
            mineFragment = MineFragment.newInstance()
            currentFragment = null
        } catch (e: Exception) {
            LogX.e("MainActivity: Fragment初始化失败: ${e.message}")
        }
    }

    private fun setupBottomNavigation() {
        // 强制禁用图标着色
        binding.bottomNavigation.itemIconTintList = null
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_home -> {
                    showFragment(homeFragment)
                    true
                }

                R.id.navigation_friend -> {
                    showFragment(relationFragment)
                    true
                }

                R.id.navigation_message -> {
                    showFragment(messageFragment)
                    true
                }

                R.id.navigation_mine -> {
                    showFragment(mineFragment)
                    true
                }

                else -> false
            }
        }

        // 设置默认选中首页
        binding.bottomNavigation.selectedItemId = R.id.navigation_home

        setBadge()
    }

    @SuppressLint("RestrictedApi")
    private fun setBadge() {
        val menuView = binding.bottomNavigation.getChildAt(0) as BottomNavigationMenuView
        val itemView03 = menuView.getChildAt(2) as BottomNavigationItemView//消息
        badgeView = LayoutInflater.from(this).inflate(R.layout.app_badge_view, menuView, false)
        badgeView?.visibility = View.GONE
        badgeView?.let {
            it.visibility = View.GONE
            itemView03.addView(it)
            //获取子view并设置显示数目
            badgeCountView = it.findViewById(R.id.tv_badge)
        }
    }

    private fun showFragment(fragment: Fragment) {
        if (currentFragment == fragment) return

        // 检查FragmentManager状态
        if (supportFragmentManager.isStateSaved) {
            LogX.w("MainActivity: FragmentManager状态已保存，跳过Fragment显示")
            return
        }

        try {
            val transaction = supportFragmentManager.beginTransaction()

            // 隐藏当前Fragment
            currentFragment?.let { transaction.hide(it) }

            // 如果Fragment还没有添加，则添加它
            if (!fragment.isAdded) {
                transaction.add(R.id.fragment_container, fragment)
            } else {
                transaction.show(fragment)
            }

            transaction.commitAllowingStateLoss() // 使用commitAllowingStateLoss避免状态异常
            currentFragment = fragment
            lifecycleScope.launch {
                tabChangedFlow.emit(currentFragment)
            }
        } catch (e: Exception) {
            LogX.e("MainActivity: Fragment显示失败: ${e.message}")
        }
    }

    /**
     * 显示底部导航栏
     */
    private fun showBottomNavigation() {
        if (::binding.isInitialized) {
            binding.bottomNavigation.visibility = View.VISIBLE
        }
    }


    @SuppressLint("RestrictedApi")
    override fun initViewEvents() {
        socketMessageViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is MessagePageEvent.RefreshBottomBarUnReadMsg -> {
                    //获取底部菜单view
                    if (it.count > 0) {
                        badgeView?.visibility = View.VISIBLE
                        badgeCountView?.text = if (it.count > 99) "99+" else it.count.toString()
                    } else {
                        badgeView?.visibility = View.GONE
                    }
                }

                else -> {}
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 5. 禁用自动弹框，清理资源
        if (::updateManager.isInitialized) {
            updateManager.disableAutoDialogs()
        }
    }
}