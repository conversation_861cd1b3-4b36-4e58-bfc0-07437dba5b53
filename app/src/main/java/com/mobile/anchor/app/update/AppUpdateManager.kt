package com.mobile.anchor.app.update

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.core.content.FileProvider
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.data.repository.CommonRepository
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.components.UpdateProgressDialog
import com.mobile.anchor.app.ui.popup.ComposeDialogFragment
import com.mobile.anchor.app.ui.popup.ComposePopup
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * 应用更新管理器
 * 统一管理应用更新的整个流程
 */
class AppUpdateManager private constructor(private val context: Context) :
    DefaultLifecycleObserver {

    companion object {
        @Volatile
        private var INSTANCE: AppUpdateManager? = null

        fun getInstance(context: Context): AppUpdateManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AppUpdateManager(context.applicationContext).also { INSTANCE = it }
            }
        }

        private const val DOWNLOAD_TIMEOUT = 40L // 秒
        private const val CONNECT_TIMEOUT = 15L // 秒
        private const val READ_TIMEOUT = 40L // 秒
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val repository = CommonRepository()
    private val notificationManager = UpdateNotificationManager(context)

    // 状态管理
    private val _updateState = MutableStateFlow<UpdateState>(UpdateState.Idle)
    val updateState: StateFlow<UpdateState> = _updateState.asStateFlow()

    private val _downloadState = MutableStateFlow<DownloadState>(DownloadState.Idle)
    val downloadState: StateFlow<DownloadState> = _downloadState.asStateFlow()

    private val _installState = MutableStateFlow<InstallState>(InstallState.Idle)
    val installState: StateFlow<InstallState> = _installState.asStateFlow()

    // 弹框管理状态
    private val _dialogState = MutableStateFlow<DialogState>(DialogState.None)
    val dialogState: StateFlow<DialogState> = _dialogState.asStateFlow()

    // 下载相关
    private var downloadCall: Call? = null
    private var downloadJob: Job? = null
    private var currentVersionInfo: VersionBean? = null

    // 弹框管理
    private var currentUpdateDialog: ComposeDialogFragment? = null
    private var currentProgressDialog: ComposeDialogFragment? = null
    private var hasUserDeclinedUpdateThisSession = false
    private var isUserTriggeredCheck = false
    private var currentActivity: FragmentActivity? = null
    private var dialogObserverJob: Job? = null
    private var isProgressDialogShowing = false

    private var userCloseProgressDialog = false

    // HTTP客户端
    private val httpClient =
        OkHttpClient.Builder().connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DOWNLOAD_TIMEOUT, TimeUnit.SECONDS).build()

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
//        checkResumeDownload()
        setupDialogStateObserver()
    }

    /**
     * 启用自动弹框显示（用于Activity和Compose环境）
     * @param activity Activity实例，用于显示弹框
     * @param isUserTriggered 是否为用户手动触发的检查更新
     */
    fun enableAutoDialogs(activity: FragmentActivity, isUserTriggered: Boolean = false) {
        LogX.d("启用自动弹框显示，isUserTriggered: $isUserTriggered")
        this.isUserTriggeredCheck = isUserTriggered
        this.currentActivity = activity

        // 取消之前的观察器
        dialogObserverJob?.cancel()

        if (!isUserTriggeredCheck) return

        // 启动新的观察器
        dialogObserverJob = scope.launch {
            dialogState.collect { state ->
                when (state) {
                    is DialogState.ShowUpdateDialog -> {
                        showUpdateDialog(activity, state.versionInfo)
                    }

                    is DialogState.ShowProgressDialog -> {
                        showProgressDialog(activity, state.downloadState)
                    }

                    is DialogState.None -> {
                        dismissAllDialogs()
                    }
                }
            }
        }
    }

    /**
     * 检查版本更新
     */
    fun checkUpdate(showNoUpdateToast: Boolean = false, isUserTriggered: Boolean = false) {
        this.isUserTriggeredCheck = isUserTriggered
        if (_updateState.value is UpdateState.CheckingUpdate) {
            LogX.d("正在检查更新中，跳过重复请求")
            return
        }

        if (_downloadState.value is DownloadState.Downloading) {
            _updateState.value = DataStoreManager.getIsolatedConfig()?.let {
                UpdateState.UpdateAvailable(
                    VersionBean(
                        update_type = it.update_type,
                        update_url = it.update_url,
                        update_tips = it.update_tips
                    )
                )
            } ?: UpdateState.Idle

            if (showNoUpdateToast) {
                ToastUtil.show(context.getString(R.string.downloading_new_version_in_progress))
            }
            return
        }

        if (_downloadState.value is DownloadState.Downloaded && isUserTriggeredCheck) {
            currentActivity?.let {
                LogX.d("显示安装弹框11111")
                showInstallDialog(_downloadState.value)
            }
            return
        }

        _updateState.value = UpdateState.CheckingUpdate

        scope.launch {
            try {
                val result = repository.checkVersion()
                result.onSuccess { versionInfo ->
                    LogX.d("版本检查成功: $versionInfo")
                    if (shouldUpdate(versionInfo)) {
                        handleVersionCheckResult(versionInfo)
                    } else {
                        _updateState.value = UpdateState.NoUpdateAvailable
                        if (showNoUpdateToast) {
                            LogX.d("已是最新版本")
                            ToastUtil.show(context.getString(R.string.already_the_latest_version))
                        }
                    }
                }.onFailure { error ->
                    LogX.e("版本检查失败", error)
                    _updateState.value = UpdateState.CheckUpdateError(
                        error.message ?: context.getString(R.string.check_update_failed)
                    )
                }
            } catch (e: Exception) {
                LogX.e("版本检查异常", e)
                _updateState.value = UpdateState.CheckUpdateError(
                    e.message ?: context.getString(R.string.check_for_abnormal_updates)
                )
            }
        }
    }

    /**
     * 检查是否需要更新
     */
    private fun shouldUpdate(versionInfo: VersionBean): Boolean {
        return versionInfo.hasUpdate
//        return when (versionInfo.update_type) {
//            1, 2 -> { // 提示更新或强制更新
//                versionInfo.version_code > BuildConfig.VERSION_CODE
//            }
//            else -> false
//        }
    }

    /**
     * 处理外部版本检查结果
     */
    private fun handleVersionCheckResult(versionInfo: VersionBean) {
        LogX.d("处理外部版本检查结果: $versionInfo")

        scope.launch {
            if (shouldUpdate(versionInfo)) {
                currentVersionInfo = versionInfo
                _updateState.value = UpdateState.UpdateAvailable(versionInfo)

                // 保存更新信息到本地
                DataStoreManager.saveIsolatedConfig(
                    com.mobile.anchor.app.data.model.IsolatedConfigBean(
                        update_type = versionInfo.update_type,
                        update_url = versionInfo.update_url,
                        update_tips = versionInfo.update_tips
                    )
                )
            } else {
                _updateState.value = UpdateState.NoUpdateAvailable
                LogX.d("当前已是最新版本")
            }
        }
    }

    /**
     * 检查是否应该显示更新提示（考虑当次会话用户是否已拒绝）
     */
    private fun shouldShowUpdatePrompt(
        versionInfo: VersionBean, hasUserDeclinedThisSession: Boolean
    ): Boolean {
        // 强制更新总是显示
        if (versionInfo.isUpdateRequired) {
            LogX.d("强制更新，必须显示提示")
            return true
        }

        // 检查用户当次会话是否已拒绝
        if (hasUserDeclinedThisSession) {
            return false
        }

        LogX.d("普通更新且用户当次会话未拒绝，显示提示")
        return true
    }

    /**
     * 显示更新弹框
     */
    private fun showUpdateDialog(activity: FragmentActivity, versionInfo: VersionBean) {
        LogX.d("显示更新弹框: ${versionInfo.version_name}")

        dismissUpdateDialog()
        userCloseProgressDialog = false
        currentUpdateDialog = ComposePopup.showUpdateDialog(
            context = activity,
            updateState = UpdateState.UpdateAvailable(versionInfo),
            dismissOnBackPress = !versionInfo.isUpdateRequired,
            dismissOnClickOutside = !versionInfo.isUpdateRequired,
            onConfirm = {
                LogX.d("用户确认更新")
                startDownload(versionInfo)
            },
            onCancel = {
                LogX.d("用户取消更新")
                if (!versionInfo.isUpdateRequired && isUserTriggeredCheck) {
                    hasUserDeclinedUpdateThisSession = true
                }
                resetStates()
            },
            onDismiss = {
                LogX.d("更新弹框被关闭")
                if (!versionInfo.isUpdateRequired && isUserTriggeredCheck) {
                    hasUserDeclinedUpdateThisSession = true
                    resetStates()
                }
            })

        LogX.d("更新弹框已显示")
    }

    /**
     * 显示下载进度弹框
     */
    private fun showProgressDialog(activity: FragmentActivity, downloadState: DownloadState) {
        LogX.d("显示下载进度弹框，当前状态: $downloadState")

        if (userCloseProgressDialog) return

        // 如果弹框已经在显示，根据状态决定是否需要重新创建
        if (isProgressDialogShowing) {
            when (downloadState) {
                is DownloadState.Downloading -> {
                    // 下载进度更新时不重新创建弹框
                    LogX.d("进度弹框已显示，跳过重复创建")
                    return
                }

                is DownloadState.DownloadError, is DownloadState.DownloadCancelled -> {
                    // 错误和取消状态需要重新创建弹框以更新内容
                    LogX.d("下载状态变为错误/取消，重新创建弹框")
                    dismissProgressDialog()
                }

                else -> {
                    LogX.d("其他状态，重新创建弹框")
                    dismissProgressDialog()
                }
            }
        }

        // 标记弹框正在显示
        isProgressDialogShowing = true
        currentProgressDialog = createProgressDialogWithStateObserver(activity, downloadState)

        LogX.d("下载进度弹框已显示")
    }

    /**
     * 创建带有状态观察的进度弹框
     */
    private fun createProgressDialogWithStateObserver(
        activity: FragmentActivity, initialState: DownloadState
    ): ComposeDialogFragment? {
        return ComposePopup.showCustomDialog(activity) { dismiss ->
            // 在弹框内部观察下载状态变化
            val currentDownloadState by downloadState.collectAsState(initialState)
            var visible by remember { mutableStateOf(true) }

            UpdateProgressDialog(
                visible = visible,
                downloadState = currentDownloadState,
                dismissOnBackPress = currentVersionInfo?.isUpdateRequired != true,
                dismissOnClickOutside = currentVersionInfo?.isUpdateRequired != true,
                autoDismiss = false,
                onClose = {
                    LogX.d("用户关闭下载弹框")
                    visible = false
                    isProgressDialogShowing = false
                    userCloseProgressDialog = true
                    dismiss()
                },
                onCancel = {
                    LogX.d("用户取消下载")
                    isProgressDialogShowing = false
                    cancelDownload()
                    dismiss()
                },
                onRetry = {
                    LogX.d("用户重试下载")
                    currentVersionInfo?.let { versionInfo ->
                        startDownload(versionInfo)
                    }
                },
                onDismiss = {
                    currentVersionInfo?.let {
                        if (!it.isUpdateRequired) {
                            LogX.d("下载进度弹框被关闭")
                            isProgressDialogShowing = false
                            dismiss()
                        }
                    }
                    // 处理下载完成后的自动安装
                    if (currentDownloadState is DownloadState.Downloaded) {
                        installApk(
                            (currentDownloadState as DownloadState.Downloaded).filePath, activity
                        )
                    }
                })
        }
    }

    private fun showInstallDialog(downloadState: DownloadState) {
        currentActivity?.let {
            ComposePopup.showConfirmDialog(
                it,
                title = context.getString(R.string.update_available_title),
                content = context.getString(R.string.new_version_install_tips),
                confirmText = context.getString(R.string.install),
                autoDismiss = currentVersionInfo?.isUpdateRequired == true || updateState.value.let { (it is UpdateState.UpdateAvailable && it.versionInfo.isUpdateRequired) },
                onConfirm = {
                    installApk(
                        (downloadState as DownloadState.Downloaded).filePath, it
                    )
                },
                showCancelButton = currentVersionInfo?.isUpdateRequired == false || updateState.value.let { (it is UpdateState.UpdateAvailable && !it.versionInfo.isUpdateRequired) },
                onCancel = {
                    hasUserDeclinedUpdateThisSession = true
                    _updateState.value = UpdateState.Idle
                })
        }
    }

    /**
     * 开始下载更新
     */
    fun startDownload(versionInfo: VersionBean) {
        LogX.d("startDownload called with versionInfo: $versionInfo")

        if (_downloadState.value is DownloadState.Downloading) {
            LogX.d("正在下载中，跳过重复请求")
            return
        }

        // 检查URL是否有效
        if (versionInfo.update_url.isBlank()) {
            LogX.e("下载URL为空")
            _downloadState.value =
                DownloadState.DownloadError(context.getString(R.string.download_url_is_empty))
            return
        }

        if (_downloadState.value is DownloadState.Downloaded) {
            LogX.d("已下载完成")
            LogX.d("显示安装弹框2222")
            showInstallDialog(_downloadState.value)
            return
        }

        currentVersionInfo = versionInfo
        LogX.d("设置当前版本信息: ${versionInfo.update_url}")

        scope.launch {
            try {
                LogX.d("开始准备下载")
                _downloadState.value = DownloadState.Preparing

                val fileName = if (versionInfo.version_name.isNotEmpty()) {
                    "anchor_${versionInfo.version_name}.apk"
                } else {
                    "anchor_update.apk"
                }
                val downloadPath = getDownloadPath()
                val file = File(downloadPath, fileName)

                LogX.d("下载文件名: $fileName")
                LogX.d("下载路径: ${file.absolutePath}")

                // 检查是否已经下载完成
                if (file.exists() && isValidApk(file)) {
                    LogX.d("发现已下载的APK文件: ${file.absolutePath}")
                    _downloadState.value = DownloadState.Downloaded(file.absolutePath)
                    return@launch
                }

                // 显示下载开始通知
                notificationManager.showDownloadStartNotification(fileName)

                // 开始下载
                LogX.d("调用downloadFile方法")
                downloadFile(versionInfo.update_url, file)

            } catch (e: Exception) {
                LogX.e("开始下载失败", e)
                _downloadState.value = DownloadState.DownloadError(
                    e.message ?: context.getString(R.string.download_preparation_failed)
                )
                notificationManager.showDownloadFailedNotification(
                    e.message ?: context.getString(R.string.download_preparation_failed)
                )
            }
        }
    }

    /**
     * 检查断点续传
     */
    private fun checkResumeDownload() {
        scope.launch {
            try {
                val isolatedConfig = DataStoreManager.getIsolatedConfig()
                if (isolatedConfig?.hasUpdate == true) {
                    val downloadPath = getDownloadPath()
                    val files = File(downloadPath).listFiles { _, name ->
                        name.startsWith("anchor_") && name.endsWith(".apk")
                    }

                    files?.forEach { file ->
                        if (isValidApk(file)) {
                            LogX.d("发现已下载的APK文件，可以直接安装: ${file.absolutePath}")
                            // 这里可以选择自动提示用户安装，或者等待用户手动触发
                            LogX.d("显示安装弹框44444")
                            _updateState.value = UpdateState.UpdateAvailable(
                                VersionBean(
                                    update_type = isolatedConfig.update_type,
                                    update_url = isolatedConfig.update_url,
                                    update_tips = isolatedConfig.update_tips
                                )
                            )
                            showInstallDialog(DownloadState.Downloaded(file.absolutePath))
                        }
                    }
                }
            } catch (e: Exception) {
                LogX.e("检查断点续传失败", e)
            }
        }
    }

    /**
     * 下载文件
     */
    private suspend fun downloadFile(url: String, file: File) = withContext(Dispatchers.IO) {
        try {
            LogX.d("开始下载文件: $url")
            LogX.d("下载路径: ${file.absolutePath}")

            // 创建目录
            file.parentFile?.mkdirs()

            // 检查断点续传
            val startPos = if (file.exists()) file.length() else 0L
            LogX.d("断点续传起始位置: $startPos")

            val request = Request.Builder().url(url).apply {
                if (startPos > 0) {
                    addHeader("Range", "bytes=$startPos-")
                    LogX.d("添加Range头: bytes=$startPos-")
                }
            }.build()

            downloadCall = httpClient.newCall(request)
            LogX.d("创建下载请求成功")

            // 直接执行同步请求，而不是使用enqueue
            val response = downloadCall?.execute()
            if (response == null) {
                throw IOException("无法创建下载请求")
            }

            LogX.d("收到响应: ${response.code}")

            if (!response.isSuccessful) {
                val errorMsg = "下载失败: ${response.code} ${response.message}"
                LogX.e(errorMsg)
                scope.launch {
                    _downloadState.value = DownloadState.DownloadError(errorMsg)
                    notificationManager.showDownloadFailedNotification(errorMsg)
                }
                return@withContext
            }

            try {
                val body = response.body
                val contentLength = response.header("Content-Length")?.toLongOrNull() ?: 0L
                val totalBytes = if (startPos > 0) startPos + contentLength else contentLength

                LogX.d("文件总大小: $totalBytes, 内容长度: $contentLength")

                val inputStream: InputStream = body.byteStream()
                val outputStream = FileOutputStream(file, startPos > 0) // 断点续传模式

                val buffer = ByteArray(8192)
                var downloadedBytes = startPos
                var lastUpdateTime = System.currentTimeMillis()
                var lastDownloadedBytes = downloadedBytes

                LogX.d("开始读取数据流")

                while (isActive) {
                    val bytesRead = inputStream.read(buffer)
                    if (bytesRead == -1) {
                        LogX.d("数据读取完成")
                        break
                    }

                    outputStream.write(buffer, 0, bytesRead)
                    downloadedBytes += bytesRead

                    // 更新进度（每500ms更新一次）
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastUpdateTime >= 500) {
                        val progress = if (totalBytes > 0) {
                            ((downloadedBytes * 100) / totalBytes).toInt()
                        } else 0

                        // 计算下载速度
                        val timeDiff = (currentTime - lastUpdateTime) / 1000.0
                        val bytesDiff = downloadedBytes - lastDownloadedBytes
                        val speed = if (timeDiff > 0) {
                            formatSpeed((bytesDiff / timeDiff).toLong())
                        } else ""

                        LogX.d("下载进度: $progress%, 速度: $speed")

                        scope.launch {
                            val downloadState = DownloadState.Downloading(
                                progress = progress,
                                downloadedBytes = downloadedBytes,
                                totalBytes = totalBytes,
                                speed = speed
                            )
                            _downloadState.value = downloadState

                            // 更新通知
                            val downloadProgress = DownloadProgress(
                                progress = progress,
                                downloadedBytes = downloadedBytes,
                                totalBytes = totalBytes,
                                speed = speed,
                                remainingTime = ""
                            )
                            notificationManager.updateDownloadProgress(downloadProgress)
                        }

                        lastUpdateTime = currentTime
                        lastDownloadedBytes = downloadedBytes
                    }
                }

                outputStream.close()
                inputStream.close()
                response.close()

                if (isActive) {
                    LogX.d("下载完成: ${file.absolutePath}")
                    scope.launch {
                        _downloadState.value = DownloadState.Downloaded(file.absolutePath)
                        notificationManager.showDownloadCompleteNotification(
                            file.absolutePath, file.name
                        )
                    }
                }

            } catch (e: Exception) {
                if (isActive) {
                    LogX.e("下载过程中出错", e)
                    scope.launch {
                        _downloadState.value = DownloadState.DownloadError(
                            e.message ?: context.getString(R.string.download_failed)
                        )
                        notificationManager.showDownloadFailedNotification(
                            e.message ?: context.getString(R.string.download_failed)
                        )
                    }
                }
            } finally {
                response.close()
            }

        } catch (e: Exception) {
            LogX.e("下载文件异常", e)
            scope.launch {
                _downloadState.value = DownloadState.DownloadError(
                    e.message ?: context.getString(R.string.download_exception)
                )
                notificationManager.showDownloadFailedNotification(
                    e.message ?: context.getString(R.string.download_exception)
                )
            }
        }
    }


    /**
     * 取消下载
     */
    fun cancelDownload() {
        downloadCall?.cancel()
        downloadJob?.cancel()

        // 清理不完整的下载文件
        currentVersionInfo?.let { versionInfo ->
            val fileName = if (versionInfo.version_name.isNotEmpty()) {
                "anchor_${versionInfo.version_name}.apk"
            } else {
                "anchor_update.apk"
            }
            val downloadPath = getDownloadPath()
            val file = File(downloadPath, fileName)

            if (file.exists()) {
                LogX.d("删除不完整的下载文件: ${file.absolutePath}")
                file.delete()
            }
        }

        _downloadState.value = DownloadState.DownloadCancelled
        // 重置更新状态，允许用户重新检查更新
        _updateState.value = UpdateState.Idle
        notificationManager.cancelNotification()
        LogX.d("下载已取消，重置更新状态")
    }

    /**
     * 设置弹框状态观察器
     */
    private fun setupDialogStateObserver() {
        scope.launch {
            updateState.collect { state ->
                when (state) {
                    is UpdateState.UpdateAvailable -> {
                        // 检查是否应该显示更新提示
                        val shouldShow = if (isUserTriggeredCheck) {
                            // 用户手动触发，总是显示
                            true
                        } else {
                            // 自动检查，需要检查用户是否已拒绝
                            shouldShowUpdatePrompt(
                                state.versionInfo, hasUserDeclinedUpdateThisSession
                            )
                        }

                        if (shouldShow) {
                            LogX.d("设置弹框状态为显示更新弹框")
                            checkResumeDownload()
                            _dialogState.value = DialogState.ShowUpdateDialog(state.versionInfo)
                        } else {
                            LogX.d("用户已拒绝更新，不显示弹框")
                        }
                    }

                    else -> {}
                }
            }
        }

        scope.launch {
            downloadState.collect { state ->
                when (state) {
                    is DownloadState.Preparing -> {
                        // 只在第一次准备时显示弹框
                        if (!isProgressDialogShowing) {
                            _dialogState.value = DialogState.ShowProgressDialog(state)
                        }
                    }

                    is DownloadState.Downloading -> {
                        // 只在第一次下载时显示弹框，后续进度更新不重新显示
                        if (!isProgressDialogShowing) {
                            _dialogState.value = DialogState.ShowProgressDialog(state)
                        }
                        // 进度更新时不触发弹框重新显示，弹框内部会自动更新
                    }

                    is DownloadState.Downloaded -> {
                        _dialogState.value = DialogState.None
//                        isProgressDialogShowing = false
                        // 自动安装
                        LogX.d("显示安装弹框5555")
//                        showInstallDialog(state)
                    }

                    is DownloadState.DownloadError, is DownloadState.DownloadCancelled -> {
                        // 错误和取消状态需要更新弹框内容
                        _dialogState.value = DialogState.ShowProgressDialog(state)
                    }

                    else -> {
                        if (state is DownloadState.Idle) {
                            _dialogState.value = DialogState.None
                            isProgressDialogShowing = false
                        }
                    }
                }
            }
        }
    }

    /**
     * 验证APK文件是否有效
     */
    private fun isValidApk(file: File): Boolean {
        return try {
            if (!file.exists() || file.length() <= 0 || !file.name.endsWith(".apk")) {
                return false
            }

            // 尝试使用PackageManager验证APK文件
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageArchiveInfo(
                    file.absolutePath, android.content.pm.PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION") packageManager.getPackageArchiveInfo(file.absolutePath, 0)
            }

            val isValid = packageInfo != null
            LogX.d("APK文件验证结果: ${file.absolutePath}, 有效: $isValid")

            if (!isValid) {
                LogX.w("APK文件无效，删除文件: ${file.absolutePath}")
                file.delete()
            }

            isValid
        } catch (e: Exception) {
            LogX.e("验证APK文件失败", e)
            // 如果验证失败，删除可能损坏的文件
            try {
                file.delete()
            } catch (deleteException: Exception) {
                LogX.e("删除损坏的APK文件失败", deleteException)
            }
            false
        }
    }

    /**
     * 安装APK
     */
    fun installApk(filePath: String, activity: Activity? = null) {
        scope.launch {
            try {
                _installState.value = InstallState.Installing

                val file = File(filePath)
                if (!file.exists()) {
                    LogX.e("安装APK失败，文件不存在: $filePath，开始重新下载")
                    _installState.value =
                        InstallState.InstallError(context.getString(R.string.apk_does_not_exist))
                    currentVersionInfo?.let {
                        resetStates()
                        startDownload(it)
                    } ?: checkResumeDownload()
                    return@launch
                }

                // 检查安装权限
                if (!UpdatePermissionManager.checkInstallPermission(context)) {
                    if (activity != null) {
                        UpdatePermissionManager.requestInstallPermission(activity) { granted ->
                            if (granted) {
                                performInstall(file)
                            } else {
                                _installState.value =
                                    InstallState.InstallError(context.getString(R.string.installation_permission_required))
                            }
                        }
                    } else {
                        _installState.value =
                            InstallState.InstallError(context.getString(R.string.installation_permission_required))
                    }
                    return@launch
                }

                performInstall(file)

            } catch (e: Exception) {
                LogX.e("安装APK失败", e)
                _installState.value = InstallState.InstallError(
                    e.message ?: context.getString(R.string.installation_failed)
                )
            }
        }
    }

    /**
     * 执行安装
     */
    private fun performInstall(file: File) {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            val apkUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                FileProvider.getUriForFile(
                    context, "${context.packageName}.fileprovider", file
                )
            } else {
                android.net.Uri.fromFile(file)
            }

            intent.setDataAndType(apkUri, "application/vnd.android.package-archive")

            // 授权给所有可能的安装器
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val resolveLists = context.packageManager.queryIntentActivities(
                    intent, android.content.pm.PackageManager.MATCH_DEFAULT_ONLY
                )
                for (resolveInfo in resolveLists) {
                    val packageName = resolveInfo.activityInfo.packageName
                    context.grantUriPermission(
                        packageName,
                        apkUri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    )
                }
            }

            context.startActivity(intent)
            _installState.value = InstallState.InstallSuccess

            // 清理通知
            notificationManager.cancelNotification()

            LogX.d("启动安装界面成功")

        } catch (e: Exception) {
            LogX.e("启动安装界面失败", e)
            _installState.value = InstallState.InstallError(
                e.message ?: context.getString(R.string.installation_failed_to_start)
            )
        }
    }

    /**
     * 重置状态
     */
    fun resetStates() {
        LogX.d("重置所有状态")
        _updateState.value = UpdateState.Idle
        _downloadState.value = DownloadState.Idle
        _installState.value = InstallState.Idle
        _dialogState.value = DialogState.None
//        currentVersionInfo = null
        userCloseProgressDialog = false

        // 取消正在进行的下载
        downloadCall?.cancel()
        downloadJob?.cancel()

        // 取消通知
        notificationManager.cancelNotification()

        // 关闭所有弹框
        dismissAllDialogs()
    }

    /**
     * 重置会话状态（应用冷启动时调用）
     */
    fun resetSessionState() {
        LogX.d("重置会话状态")
        hasUserDeclinedUpdateThisSession = false
        isUserTriggeredCheck = false
    }

    /**
     * 关闭更新弹框
     */
    private fun dismissUpdateDialog() {
        runCatching {
            if (currentUpdateDialog?.isAdded == false || currentUpdateDialog?.isDetached == true) return
            currentUpdateDialog?.dismiss()
            currentUpdateDialog = null
        }
    }

    /**
     * 关闭进度弹框
     */
    private fun dismissProgressDialog() {
        runCatching {
            if (currentUpdateDialog?.isAdded == false || currentUpdateDialog?.isDetached == true) return
            currentProgressDialog?.dismiss()
            currentProgressDialog = null
            isProgressDialogShowing = false
        }
    }

    /**
     * 关闭所有弹框
     */
    private fun dismissAllDialogs() {
        dismissUpdateDialog()
        dismissProgressDialog()
    }

    /**
     * 禁用自动弹框显示
     */
    fun disableAutoDialogs() {
        LogX.d("禁用自动弹框显示")
        dialogObserverJob?.cancel()
        dialogObserverJob = null
        currentActivity = null
        isProgressDialogShowing = false
        dismissAllDialogs()
    }

    /**
     * 获取用户是否已拒绝更新（当前会话）
     */
    fun hasUserDeclinedThisSession(): Boolean {
        return hasUserDeclinedUpdateThisSession
    }

    /**
     * 获取下载路径
     */
    private fun getDownloadPath(): String {
        // 使用应用的缓存目录，避免FileProvider配置问题
        val cacheDir = File(context.cacheDir, "apk_downloads")
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return cacheDir.absolutePath
    }


    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        cancelDownload()
        dismissAllDialogs()
        scope.cancel()
    }

    /**
     * 格式化下载速度
     */
    private fun formatSpeed(bytesPerSecond: Long): String {
        return when {
            bytesPerSecond < 1024 -> "${bytesPerSecond}B/s"
            bytesPerSecond < 1024 * 1024 -> "${bytesPerSecond / 1024}KB/s"
            else -> String.format("%.1fMB/s", bytesPerSecond / (1024.0 * 1024.0))
        }
    }
}
