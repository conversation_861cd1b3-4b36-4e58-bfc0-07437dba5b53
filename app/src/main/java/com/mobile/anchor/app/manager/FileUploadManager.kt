package com.mobile.anchor.app.manager

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaCodecInfo
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.OpenableColumns
import android.webkit.MimeTypeMap
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.transformer.Composition
import androidx.media3.transformer.DefaultEncoderFactory
import androidx.media3.transformer.EditedMediaItem
import androidx.media3.transformer.ExportException
import androidx.media3.transformer.ExportResult
import androidx.media3.transformer.Transformer
import androidx.media3.transformer.VideoEncoderSettings
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.data.repository.FileRepository
import com.mobile.anchor.app.logger.LogX
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okio.BufferedSink
import okio.source
import java.io.File
import java.io.FileOutputStream
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * 统一文件上传管理器
 *
 * 功能特性：
 * - 支持多种文件类型（图片、视频、音频、文档、日志）
 * - 统一的上传接口，自动处理文件类型识别
 * - 支持File和Uri两种输入方式
 * - 完整的错误处理和日志记录
 * - 可配置的HTTP客户端
 * - 支持自定义文件名和业务类型
 *
 * 使用示例：
 * ```kotlin
 * // 上传图片并返回URL
 * FileUploadManager.upload(context, imageUri).collect { result ->
 *     when (result) {
 *         is ApiResult.Success -> println("上传成功: ${result.data.accessUrl}")
 *         is ApiResult.Error -> println("上传失败: ${result.message}")
 *     }
 * }
 *
 * // 上传日志文件
 * FileUploadManager.upload(
 *     file = logFile,
 *     fileType = FileType.LOG,
 *     bizType = BizType.LOG_UPLOAD
 * )
 * ```
 */
object FileUploadManager {

    private val fileRepository = FileRepository()

    // 默认HTTP客户端
    private val defaultHttpClient = OkHttpClient.Builder().connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build()

    // ==================== 枚举定义 ====================

    /**
     * 文件类型枚举
     */
    enum class FileType(val extension: String, val mimeTypePrefix: String) {
        IMAGE(".png", "image/"), VIDEO(".mp4", "video/"), AUDIO(".mp3", "audio/"), DOCUMENT(
            ".doc", "application/"
        ),
        LOG(".log", "text/")
    }

    /**
     * 业务类型枚举
     */
    enum class BizType(val value: Int) {
        NORMAL_UPLOAD(0),    // 普通业务上传
        LOG_UPLOAD(1)        // 日志上传
    }

    /**
     * 上传配置
     */
    data class UploadConfig(
        val fileType: FileType? = null,        // 文件类型（null时自动识别）
        val bizType: BizType = BizType.NORMAL_UPLOAD,  // 业务类型
        val customFileName: String? = null,     // 自定义文件名
        val useLogClient: Boolean = false       // 是否使用日志专用客户端
    )

    /**
     * 上传源密封类 - 统一处理File和Uri两种输入
     */
    private sealed class UploadSource {
        data class FileSource(val file: File) : UploadSource()
        data class UriSource(val context: Context, val uri: Uri) : UploadSource()
    }

    private data class FileInfo(
        val name: String, val size: Long, val mimeType: String
    )

    data class FileUploadResult(
        val success: Boolean,
        val objectKey: String? = null,
        val accessUrl: String? = null,
        val errorMessage: String? = null
    )

    /**
     * 文件上传进度
     */
    data class UploadProgress(
        val bytesUploaded: Long,
        val totalBytes: Long,
        val percentage: Int = ((bytesUploaded * 100) / totalBytes).toInt()
    )

    data class UploadCredentialBean(
        val uploadURL: String,
        val method: String,
        val objectKey: String,
        val url: String,
    )


    // ==================== 统一上传接口 ====================

    fun upload(
        context: Context,
        uri: Uri,
        fileType: FileType = FileType.IMAGE,
        customFileName: String? = null
    ): Flow<ApiResult<FileUploadResult>> =
        upload(context, uri, UploadConfig(fileType, customFileName = customFileName))

    /**
     * 上传日志文件
     */
    suspend fun uploadLog(logFile: File): ApiResult<FileUploadResult> = upload(
        file = logFile, config = UploadConfig(
            fileType = FileType.LOG, bizType = BizType.LOG_UPLOAD, useLogClient = true
        )
    )

    /**
     * 统一上传方法 - Uri版本
     * @param context 上下文
     * @param fileUri 文件URI
     * @param config 上传配置
     * @return 上传结果流
     */
    fun upload(
        context: Context, fileUri: Uri, config: UploadConfig = UploadConfig()
    ): Flow<ApiResult<FileUploadResult>> = flow {
        emit(ApiResult.Loading)

        val result = uploadInternal(
            source = UploadSource.UriSource(context, fileUri), config = config
        )

        emit(result)
    }.flowOn(Dispatchers.IO)

    /**
     * 统一上传方法 - File版本（主要用于日志文件）
     * @param file 文件对象
     * @param config 上传配置
     * @return 上传结果
     */
    suspend fun upload(
        file: File, config: UploadConfig = UploadConfig()
    ): ApiResult<FileUploadResult> = uploadInternal(
        source = UploadSource.FileSource(file), config = config
    )

    /**
     * 通用的URL提取方法
     */
    private fun extractUrlFromResult(uploadFlow: Flow<ApiResult<FileUploadResult>>): Flow<ApiResult<String>> {
        return uploadFlow.map { result ->
            when (result) {
                is ApiResult.Success -> {
                    if (result.data.success && result.data.accessUrl != null) {
                        ApiResult.Success(result.data.accessUrl)
                    } else {
                        ApiResult.Error(message = result.data.errorMessage ?: "Upload failed")
                    }
                }

                is ApiResult.Error -> result
                is ApiResult.Loading -> result
            }
        }
    }

    // ==================== 核心私有方法 ====================
    /**
     * 核心上传方法 - 统一处理所有上传逻辑
     */
    private suspend fun uploadInternal(
        source: UploadSource, config: UploadConfig
    ): ApiResult<FileUploadResult> = withContext(Dispatchers.IO) {
        try {
            // 1. 智能源类型优化 - 将file:// URI转换为FileSource以获得更好的性能和可靠性
            val optimizedSource = optimizeUploadSource(source)

            // 2. 预处理和验证
            val (fileType, mimeType, logInfo) = when (optimizedSource) {
                is UploadSource.FileSource -> {
                    val file = optimizedSource.file
                    // 检查文件
                    if (!file.exists()) {
                        return@withContext ApiResult.Error(message = "file does not exist: ${file.absolutePath}")
                    }
                    if (file.length() == 0L) {
                        return@withContext ApiResult.Error(message = "The file is empty: ${file.absolutePath}")
                    }

                    val fileType = config.fileType ?: detectFileTypeFromExtension(file.extension)
                    val mimeType = getMimeTypeFromFile(file)
                    val logInfo = "路径: ${file.absolutePath}, 大小: ${file.length()}"

                    Triple(fileType, mimeType, logInfo)
                }

                is UploadSource.UriSource -> {
                    val fileInfo = getFileInfoEnhanced(
                        optimizedSource.context, optimizedSource.uri, config.customFileName
                    )

                    // 验证文件大小
                    if (fileInfo.size <= 0L) {
                        return@withContext ApiResult.Error(message = "Unable to retrieve file size or file is empty: ${optimizedSource.uri}")
                    }

                    val fileType = config.fileType ?: detectFileType(fileInfo.mimeType)
                    val logInfo =
                        "URI: ${optimizedSource.uri}, 名称: ${fileInfo.name}, 大小: ${fileInfo.size}"

                    Triple(fileType, fileInfo.mimeType, logInfo)
                }
            }

            LogX.d("FileUploadManager: 准备上传文件 - $logInfo, 类型: $fileType")

            // 3. 获取上传凭证
            val credentialsResult = getUploadCredentials(config.bizType, fileType)

            when (credentialsResult) {
                is ApiResult.Success -> {
                    val credentials = credentialsResult.data
                    LogX.d("FileUploadManager: 获取上传凭证成功 - URL: ${credentials.uploadURL}")

                    // 3.5. 压缩文件（如果需要）
                    val realSource = when (optimizedSource) {
                        is UploadSource.FileSource -> {
                            optimizedSource
                        }

                        is UploadSource.UriSource -> {
                            val compressed = compressFileIfNeeded(
                                context = optimizedSource.context,
                                uri = optimizedSource.uri,
                                fileType = fileType
                            )

                            LogX.d("FileUploadManager: 压缩结果 - ${compressed?.absolutePath ?: "未压缩"}")

                            if (compressed != null && compressed.exists()) {
                                UploadSource.FileSource(compressed)
                            } else {
                                optimizedSource
                            }
                        }
                    }

                    val isCompressed = realSource != optimizedSource
                    LogX.d("FileUploadManager: 是否压缩: $isCompressed $realSource")

                    // 4. 执行S3上传 - 使用优化后的源
                    val uploadResult = uploadToS3(
                        source = realSource,
                        credentials = credentials,
                        mimeType = mimeType,
                    )

                    return@withContext when (uploadResult) {
                        is ApiResult.Success -> {
                            LogX.d("FileUploadManager: 文件上传完成")
                            uploadResult
                        }

                        is ApiResult.Error -> uploadResult
                        is ApiResult.Loading -> ApiResult.Error(message = "Upload timeout")
                    }
                }

                is ApiResult.Error -> {
                    return@withContext ApiResult.Error(message = "Failed to obtain and upload credentials: ${credentialsResult.message}")
                }

                is ApiResult.Loading -> {
                    return@withContext ApiResult.Error(message = "Timeout for obtaining and uploading credentials")
                }
            }
        } catch (e: Exception) {
            LogX.e("FileUploadManager: 文件上传异常", e)
            return@withContext ApiResult.Error(
                exception = e, message = "File upload failed: ${e.message}"
            )
        }
    }

    /**
     * 获取上传凭证
     */
    private suspend fun getUploadCredentials(
        bizType: BizType, fileType: FileType
    ): ApiResult<UploadCredentialBean> {
        return fileRepository.getUploadUrl(bizType, fileType)
    }

    /**
     * 统一的S3上传方法 - 支持File和Uri两种输入源
     */
    private suspend fun uploadToS3(
        source: UploadSource,
        credentials: UploadCredentialBean,
        mimeType: String,
    ): ApiResult<FileUploadResult> = withContext(Dispatchers.IO) {
        try {
            // 根据不同的源创建RequestBody
            val requestBody = when (source) {
                is UploadSource.FileSource -> {
                    source.file.asRequestBody(mimeType.toMediaType())
                }

                is UploadSource.UriSource -> {
                    val contentResolver = source.context.contentResolver
                    val inputStream = contentResolver.openInputStream(source.uri)
                        ?: return@withContext ApiResult.Error(message = "Unable to read file")

                    // 使用增强的文件信息获取方法
                    val fileInfo = getFileInfoEnhanced(source.context, source.uri, null)
                    val fileSize = fileInfo.size

                    if (fileSize <= 0L) {
                        inputStream.close()
                        return@withContext ApiResult.Error(message = "Unable to retrieve file size or file is empty")
                    }

                    object : RequestBody() {
                        override fun contentType() = mimeType.toMediaType()

                        override fun writeTo(sink: BufferedSink) {
                            inputStream.use { input ->
                                sink.writeAll(input.source())
                            }
                        }

                        override fun contentLength(): Long = fileSize
                    }
                }
            }

            // 构建请求
            val requestBuilder = Request.Builder().url(credentials.uploadURL)
                .method(credentials.method.uppercase(), requestBody)
                .addHeader("Content-Type", mimeType)

            // 添加Content-Length头 - 对所有源类型都设置
            val contentLength = when (source) {
                is UploadSource.FileSource -> source.file.length()
                is UploadSource.UriSource -> {
                    val fileInfo = getFileInfoEnhanced(source.context, source.uri, null)
                    fileInfo.size
                }
            }

            if (contentLength > 0) {
                requestBuilder.addHeader("Content-Length", contentLength.toString())
            }

            val request = requestBuilder.build()

            // 选择HTTP客户端并执行请求
            val response = defaultHttpClient.newCall(request).execute()

            // 处理响应
            val result = if (response.isSuccessful) {
                val sourceName = when (source) {
                    is UploadSource.FileSource -> {
                        val fileName = source.file.name
                        // 删除临时压缩文件
                        if (fileName.contains("compressed_video_") || fileName.contains("compressed_image_")) {
                            LogX.d("FileUploadManager: 删除临时压缩文件: $fileName")
                            source.file.delete()
                        }
                        fileName
                    }

                    is UploadSource.UriSource -> "Uri文件"
                }
                LogX.d("FileUploadManager: S3上传成功 - $sourceName, 响应码: ${response.code}")
                ApiResult.Success(
                    FileUploadResult(
                        success = true,
                        objectKey = credentials.objectKey,
                        accessUrl = credentials.url
                    )
                )
            } else {
                val errorBody = response.body?.string() ?: "Unknown error"
                LogX.e("FileUploadManager: S3上传失败 - ${response.code}: $errorBody")
                ApiResult.Error(
                    message = "Upload failed: ${response.code} - $errorBody"
                )
            }

            response.close()
            result

        } catch (e: Exception) {
            LogX.e("FileUploadManager: S3上传异常", e)
            ApiResult.Error(
                exception = e, message = "Upload exception: ${e.message}"
            )
        }
    }

    /**
     * 智能优化上传源 - 将file:// URI转换为FileSource以获得更好的性能
     */
    private fun optimizeUploadSource(source: UploadSource): UploadSource {
        return when (source) {
            is UploadSource.FileSource -> source // 已经是FileSource，无需优化
            is UploadSource.UriSource -> {
                val uri = source.uri
                // 检查是否为file:// URI
                if (uri.scheme == "file" && uri.path != null) {
                    try {
                        val file = File(uri.path!!)
                        if (file.exists() && file.isFile) {
                            return UploadSource.FileSource(file)
                        }
                    } catch (e: Exception) {
                        LogX.w("FileUploadManager: file:// URI转换失败，继续使用UriSource - ${e.message}")
                    }
                }
                source // 保持原有的UriSource
            }
        }
    }

    /**
     * 根据文件类型进行压缩（图片或视频）
     */
    private suspend fun compressFileIfNeeded(
        context: Context, uri: Uri, fileType: FileType
    ): File? = withContext(Dispatchers.IO) {
        val fileInfo = getFileInfoEnhanced(context, uri, null)
        val originalSizeBytes = fileInfo.size

        // 检查文件大小阈值（5MB）
        if (originalSizeBytes <= 5 * 1024 * 1024) {
            LogX.w("compressFileIfNeeded: 文件小于5MB，跳过压缩 -> $originalSizeBytes bytes")
            return@withContext null
        }

        return@withContext when (fileType) {
            FileType.IMAGE -> compressImage(context, uri, originalSizeBytes)
            FileType.VIDEO -> compressVideo(context, uri, originalSizeBytes)
            else -> {
                LogX.w("compressFileIfNeeded: 不支持的文件类型，跳过压缩 -> $fileType")
                null
            }
        }
    }

    /**
     * 压缩图片
     */
    private suspend fun compressImage(
        context: Context, uri: Uri, originalSizeBytes: Long
    ): File? = withContext(Dispatchers.IO) {
        LogX.d("FileUploadManager: 开始压缩图片 - 大小: ${originalSizeBytes / 1024 / 1024}MB")

        try {
            val outputFile = File(context.cacheDir, "compressed_image_${UUID.randomUUID()}.jpg")

            // 使用 ContentResolver 读取图片
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: return@withContext null

            // 先获取图片尺寸信息，不加载到内存
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeStream(inputStream, null, options)
            inputStream.close()

            val originalWidth = options.outWidth
            val originalHeight = options.outHeight

            LogX.d("FileUploadManager: 原图尺寸 - ${originalWidth}x${originalHeight}")

            // 计算压缩比例
            val maxSize = when (val sizeMB = originalSizeBytes / 1024 / 1024) {
                in 0..10 -> 1920 // 10MB以下，最大1920px
                in 11..20 -> 1600 // 20MB以下，最大1600px
                in 21..50 -> 1280 // 50MB以下，最大1280px
                else -> 1024 // 50MB以上，最大1024px
            }

            val scale = calculateImageScale(originalWidth, originalHeight, maxSize)

            // 重新打开输入流进行实际解码
            val newInputStream = context.contentResolver.openInputStream(uri)
                ?: return@withContext null

            val decodeOptions = BitmapFactory.Options().apply {
                inSampleSize = scale
                inPreferredConfig = Bitmap.Config.RGB_565 // 使用RGB_565减少内存占用
            }

            val bitmap = BitmapFactory.decodeStream(newInputStream, null, decodeOptions)
            newInputStream.close()

            if (bitmap == null) {
                LogX.e("FileUploadManager: 图片解码失败")
                return@withContext null
            }

            // 计算压缩质量
            val quality = when (val sizeMB = originalSizeBytes / 1024 / 1024) {
                in 0..10 -> 85
                in 11..20 -> 75
                in 21..50 -> 65
                else -> 55
            }

            // 保存压缩后的图片
            FileOutputStream(outputFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            }

            bitmap.recycle()

            val compressedSize = outputFile.length()
            val ratio = ((originalSizeBytes - compressedSize) * 100 / originalSizeBytes).toInt()

            // 更精确的大小显示
            val originalSizeStr = if (originalSizeBytes >= 1024 * 1024) {
                "${originalSizeBytes / 1024 / 1024} MB"
            } else {
                "${originalSizeBytes / 1024} KB"
            }

            val compressedSizeStr = if (compressedSize >= 1024 * 1024) {
                "${compressedSize / 1024 / 1024} MB"
            } else {
                "${compressedSize / 1024} KB"
            }

            LogX.i("FileUploadManager: 图片压缩完成 - $originalSizeStr -> $compressedSizeStr (${ratio}%)")

            // 检查压缩效果
            when {
                compressedSize < 100L -> {
                    LogX.w("FileUploadManager: 压缩图片太小，使用原图")
                    outputFile.delete()
                    null
                }
                ratio < 5 -> {
                    LogX.w("FileUploadManager: 图片压缩效果差（$ratio%），使用原图")
                    outputFile.delete()
                    null
                }
                else -> outputFile
            }

        } catch (e: Exception) {
            LogX.e("FileUploadManager: 图片压缩失败 - ${e.message}", e)
            null
        }
    }

    /**
     * 计算图片缩放比例
     */
    private fun calculateImageScale(width: Int, height: Int, maxSize: Int): Int {
        var scale = 1
        val maxDimension = maxOf(width, height)

        while (maxDimension / scale > maxSize) {
            scale *= 2
        }

        return scale
    }

    /**
     * 压缩视频
     */
    private suspend fun compressVideo(
        context: Context, uri: Uri, originalSizeBytes: Long
    ): File? = withContext(Dispatchers.IO) {

        LogX.d("FileUploadManager: 开始压缩视频 - 大小: ${originalSizeBytes / 1024 / 1024}MB")

        // 检查视频时长
        runCatching {
            MediaMetadataRetriever().apply {
                setDataSource(context, uri)
            }.use {
                val duration =
                    it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull()
                        ?: 0L
                if (duration < 2000) {
                    LogX.w("compressVideo: 视频时长过短（${duration}ms），跳过")
                    return@withContext null
                }
            }
        }.onFailure {
            LogX.w("compressVideo: 获取时长失败: ${it.message}")
        }

        val compressedFile = CompletableDeferred<File?>()
        val outputFile = File(context.cacheDir, "compressed_video_${UUID.randomUUID()}.mp4")

        withContext(Dispatchers.Main) {
            runCatching {
                val targetBitrate = when (val sizeMB = originalSizeBytes / 1024 / 1024) {
                    in 0..20 -> 1_500_000
                    in 21..50 -> 1_200_000
                    in 51..100 -> 800_000
                    else -> 500_000
                }

                LogX.d("FileUploadManager: 目标比特率: $targetBitrate bps")

                val encoderSettings = VideoEncoderSettings.Builder().setBitrate(targetBitrate)
                    .setBitrateMode(MediaCodecInfo.EncoderCapabilities.BITRATE_MODE_VBR).build()

                val mediaItem = MediaItem.fromUri(uri)
                val edited = EditedMediaItem.Builder(mediaItem).setRemoveAudio(false).build()

                Transformer.Builder(context).setEncoderFactory(
                    DefaultEncoderFactory.Builder(context)
                        .setRequestedVideoEncoderSettings(encoderSettings).build()
                ).setVideoMimeType(MimeTypes.VIDEO_H264).addListener(object : Transformer.Listener {
                    override fun onCompleted(c: Composition, r: ExportResult) {
                        val ratio =
                            ((originalSizeBytes - r.fileSizeBytes) * 100 / originalSizeBytes).toInt()
                        val compressedMB = r.fileSizeBytes / 1024 / 1024

                        LogX.i("FileUploadManager: 视频压缩完成 - ${originalSizeBytes / 1024 / 1024} MB -> $compressedMB MB (${ratio}%)")

                        when {
                            r.fileSizeBytes < 100L -> {
                                LogX.w("FileUploadManager: 压缩视频太小，使用原视频")
                                outputFile.delete()
                                compressedFile.complete(null)
                            }

                            ratio < 5 -> {
                                LogX.w("FileUploadManager: 视频压缩效果差（$ratio%），使用原视频")
                                outputFile.delete()
                                compressedFile.complete(null)
                            }

                            else -> compressedFile.complete(outputFile)
                        }
                    }

                    override fun onError(c: Composition, r: ExportResult, e: ExportException) {
                        LogX.e("FileUploadManager: 视频压缩失败 - ${e.message}")
                        outputFile.delete()
                        compressedFile.complete(null)
                    }
                }).build().start(edited, outputFile.path)

            }.onFailure {
                LogX.e("FileUploadManager: 视频压缩初始化失败: ${it.message}")
                outputFile.delete()
                compressedFile.complete(null)
            }
        }

        LogX.d("FileUploadManager: 等待视频压缩完成...")

        return@withContext withTimeoutOrNull(120_000) {
            compressedFile.await()
        }?.also {
            LogX.d("FileUploadManager: 视频压缩成功 -> ${it.absolutePath}")
        } ?: run {
            LogX.e("FileUploadManager: 视频压缩超时或失败，删除临时文件")
            outputFile.delete()
            null
        }
    }


    /**
     * 增强的文件信息获取方法 - 改进文件大小获取逻辑
     */
    private fun getFileInfoEnhanced(
        context: Context, fileUri: Uri, customFileName: String?
    ): FileInfo {
        val contentResolver = context.contentResolver
        var fileName = customFileName
        var fileSize = 0L
        val mimeType = contentResolver.getType(fileUri) ?: "application/octet-stream"

        // 方法1: 尝试通过ContentResolver查询获取文件信息
        try {
            contentResolver.query(fileUri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)

                    if (nameIndex != -1 && fileName == null) {
                        fileName = cursor.getString(nameIndex)
                    }
                    if (sizeIndex != -1) {
                        val size = cursor.getLong(sizeIndex)
                        if (size > 0) {
                            fileSize = size
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogX.w("FileUploadManager: ContentResolver查询失败 - ${e.message}")
        }

        // 方法2: 如果文件大小仍然为0，尝试通过InputStream获取
        if (fileSize <= 0L) {
            try {
                contentResolver.openInputStream(fileUri)?.use { inputStream ->
                    fileSize = inputStream.available().toLong()
                }
            } catch (e: Exception) {
                LogX.w("FileUploadManager: InputStream获取文件大小失败 - ${e.message}")
            }
        }

        // 如果还是没有文件名，生成一个
        if (fileName == null) {
            val extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType) ?: ""
            fileName = "file_${UUID.randomUUID()}.$extension"
        }

        return FileInfo(
            name = fileName, size = fileSize, mimeType = mimeType
        )
    }

    /**
     * 统一的文件类型检测器
     */
    private object FileTypeDetector {

        // 扩展名到文件类型的映射
        private val extensionMap = mapOf(
            // 图片
            "jpg" to FileType.IMAGE,
            "jpeg" to FileType.IMAGE,
            "png" to FileType.IMAGE,
            "gif" to FileType.IMAGE,
            "bmp" to FileType.IMAGE,
            "webp" to FileType.IMAGE,
            // 视频
            "mp4" to FileType.VIDEO,
            "avi" to FileType.VIDEO,
            "mov" to FileType.VIDEO,
            "wmv" to FileType.VIDEO,
            "flv" to FileType.VIDEO,
            "mkv" to FileType.VIDEO,
            // 音频
            "mp3" to FileType.AUDIO,
            "wav" to FileType.AUDIO,
            "aac" to FileType.AUDIO,
            "flac" to FileType.AUDIO,
            "ogg" to FileType.AUDIO,
            // 日志
            "log" to FileType.LOG,
            "txt" to FileType.LOG
        )

        // MIME类型前缀到文件类型的映射
        private val mimeTypeMap = mapOf(
            "image/" to FileType.IMAGE,
            "video/" to FileType.VIDEO,
            "audio/" to FileType.AUDIO,
            "text/" to FileType.LOG
        )

        /**
         * 根据扩展名检测文件类型
         */
        fun detectByExtension(extension: String): FileType {
            return extensionMap[extension.lowercase()] ?: FileType.DOCUMENT
        }

        /**
         * 根据MIME类型检测文件类型
         */
        fun detectByMimeType(mimeType: String): FileType {
            return mimeTypeMap.entries.find { mimeType.startsWith(it.key) }?.value
                ?: FileType.DOCUMENT
        }

        /**
         * 获取文件的MIME类型
         */
        fun getMimeType(extension: String): String {
            val cleanExtension = extension.lowercase()
            return MimeTypeMap.getSingleton().getMimeTypeFromExtension(cleanExtension)
                ?: when (cleanExtension) {
                    "log" -> "text/plain; charset=utf-8"
                    else -> "application/octet-stream"
                }
        }

        /**
         * 获取文件的MIME类型（File版本）
         */
        fun getMimeType(file: File): String = getMimeType(file.extension)
    }

    // 兼容性方法
    private fun detectFileType(mimeType: String): FileType =
        FileTypeDetector.detectByMimeType(mimeType)

    private fun detectFileTypeFromExtension(extension: String): FileType =
        FileTypeDetector.detectByExtension(extension)

    private fun getMimeTypeFromFile(file: File): String = FileTypeDetector.getMimeType(file)
}
