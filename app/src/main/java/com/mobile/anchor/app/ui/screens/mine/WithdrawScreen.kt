package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.WalletCurrencyBean
import com.mobile.anchor.app.data.network.ktnet.error.msg
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.viewmodels.WalletViewModel
import com.mobile.anchor.app.utils.Constants
import kotlin.math.ceil

/**
 * 提现页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WithdrawScreen(
    navController: NavController, viewModel: WalletViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    val anchorConfig by DataStoreManager.getAnchorConfigBeanFlow().collectAsState(null)
    var withdrawAmount by remember { mutableStateOf(0) }
    var withdrawAmountText by remember { mutableStateOf(TextFieldValue("")) }
    var isLoading by remember { mutableStateOf(false) }
    val walletInfo by viewModel.walletInfo.collectAsState()

    // 货币相关状态
    val currencyList by viewModel.currencyList.collectAsState()
    val currentCurrency by viewModel.currentCurrency.collectAsState()
    var showCurrencyDialog by remember { mutableStateOf(false) }

    val withdrawInfo by viewModel.walletCurrencyBean.collectAsState()
    val minWithdrawAmount = (withdrawInfo?.min_with_draw_coin ?: 0)
    val maxWithdrawAmount = anchorConfig?.withdrawMaxCoin ?: 9999999
    val withdrawFeeRate = withdrawInfo?.set_fee_rate ?: 0F
    val withdrawFee = withdrawInfo?.set_fee_amount ?: 0
    val withdrawType = withdrawInfo?.set_fee_type ?: 0

    LaunchedEffect(Unit) {
        viewModel.getWalletInfo()
        viewModel.getWithdrawInfo()
        viewModel.getSupportedCurrencies()
        viewModel.loadCurrentCurrency()
    }

    LaunchedEffect(withdrawInfo) {
        withdrawAmount = withdrawInfo?.balance ?: 0
    }

    LaunchedEffect(uiState) {
        isLoading = false
        uiState.withdrawState.let { withdrawState ->
            if (withdrawState is PageState.Error) {
                ToastUtil.show(withdrawState.throwable.msg)
            } else if (withdrawState is PageState.Success) {
                ToastUtil.show(context.getString(R.string.withdrawal_successful))
                navController.popBackStack()
            }
        }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = stringResource(R.string.withdraw),
                onNavigationClick = { navController.popBackStack() })
        }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            // 余额卡片
            BalanceCard(
                availableBalance = walletInfo?.balance?.toShowDiamond() ?: 0,
                currencyList = currencyList,
                currentCurrency = withdrawInfo?.currency ?: "",
                onCurrencyClick = {
                    showCurrencyDialog = true
                })

//            Spacer(modifier = Modifier.height(24.dp))

            // 提现表单
//            val focusRequester = remember { FocusRequester() }
//            WithdrawForm(
//                withdrawAmount = withdrawAmount,
//                withdrawAmountText = withdrawAmountText,
//                onAmountChange = { amount ->
//                    withdrawAmount = amount
//                    val text = if (amount == 0) "" else amount.toString()
//                    withdrawAmountText = TextFieldValue(
//                        text = text, selection = TextRange(text.length)
//                    )
//                },
//                onTextChange = { textFieldValue ->
//                    withdrawAmountText = textFieldValue
//                    withdrawAmount = textFieldValue.text.toIntOrNull() ?: 0
//                },
//                minAmount = minWithdrawAmount,
//                maxAmount = maxWithdrawAmount,
//                availableBalance = walletInfo?.balance?.toShowDiamond() ?: 0,
//                focusRequester = focusRequester
//            )

            Spacer(modifier = Modifier.height(24.dp))

            AnimatedVisibility(withdrawAmount >= minWithdrawAmount) {
                // 费用信息
                FeeInfoCard(
                    withdrawAmount = withdrawAmount,
                    feeRate = withdrawFeeRate,
                    fee = withdrawFee,
                    feeType = withdrawType,
                    currencyList = currencyList,
                    currentCurrency = currentCurrency
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 提现须知
            withdrawInfo?.withdraw_rule?.takeIf { it.isNotEmpty() }?.let {
                WithdrawNotice(it)
            }

            Spacer(modifier = Modifier.height(32.dp))
            AnimatedVisibility(withdrawAmount >= minWithdrawAmount) {
                AnchorButton(
                    text = stringResource(R.string.confirm_withdraw),
                    onClick = {
                        isLoading = true
                        viewModel.withdraw(withdrawAmount)
                    },
                    enabled = withdrawInfo?.isEnabled == true && isValidAmount(
                        withdrawAmount,
                        minWithdrawAmount,
                        maxWithdrawAmount,
                        walletInfo?.balance ?: 0
                    ),
                    isLoading = isLoading,
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                Spacer(modifier = Modifier.height(32.dp))
            }
        }

        // 货币选择弹窗
        BottomSelectDialog(
            visible = showCurrencyDialog,
            title = stringResource(R.string.select_currency),
            options = currencyList.map { currencyBean ->
                "${viewModel.getCurrencyDisplayName(currencyBean.currency)} (${
                    viewModel.getCurrencySymbol(
                        currencyBean.currency
                    )
                })"
            },
            onDismiss = { showCurrencyDialog = false },
            onOptionSelected = { index, _ ->
                viewModel.switchCurrency(currencyList[index])
                showCurrencyDialog = false
            })
    }
}

/**
 * 余额卡片
 */
@Composable
private fun BalanceCard(
    availableBalance: Int,
    currencyList: List<WalletCurrencyBean> = emptyList(),
    currentCurrency: String = "USD",
    onCurrencyClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF9F2AF8), Color(0xFFB347F5)
                        )
                    )
                )
                .padding(16.dp)
        ) {
            Column {
                Text(
                    text = stringResource(R.string.available_balance),
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White.copy(alpha = 0.9f)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 钻石余额
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "$availableBalance",
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            fontSize = 28.sp
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Image(
                            painter = painterResource(R.mipmap.ic_coin),
                            contentDescription = "钻石",
                            modifier = Modifier.size(20.dp)
                        )
                    }

                    Text(
                        "≈",
                        color = Color.White,
                        fontSize = 32.sp,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )

                    // 货币转换显示
                    val currentCurrencyData = currencyList.find { it.currency == currentCurrency }
                    val convertedAmount = currentCurrencyData?.let {
                        (availableBalance * it.to_currency_money).toInt()
                    } ?: 0

                    Text(
                        text = "${Constants.Currency.getSymbol(currentCurrency)}$convertedAmount",
                        style = MaterialTheme.typography.headlineLarge,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        fontSize = 28.sp
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    // 货币切换按钮
//                    TextButton(
//                        onClick = onCurrencyClick,
//                        modifier = Modifier.height(28.dp),
//                        contentPadding = PaddingValues(horizontal = 6.dp),
//                        colors = ButtonDefaults.textButtonColors(
//                            contentColor = Color.White.copy(alpha = 0.8f)
//                        )
//                    ) {
//                        Row(
//                            verticalAlignment = Alignment.CenterVertically
//                        ) {
//                            Text(
//                                text = currentCurrency,
//                                style = MaterialTheme.typography.titleMedium,
//                                fontSize = 16.sp
//                            )
//
//                            Icon(
//                                imageVector = Icons.Default.KeyboardArrowRight,
//                                contentDescription = "切换货币",
//                                modifier = Modifier.size(16.dp),
//                                tint = Color.White.copy(alpha = 0.8f)
//                            )
//                        }
//                    }
                }
            }
        }
    }
}

/**
 * 提现表单
 */
@Composable
private fun WithdrawForm(
    withdrawAmount: Int,
    withdrawAmountText: TextFieldValue,
    onAmountChange: (Int) -> Unit,
    onTextChange: (TextFieldValue) -> Unit,
    minAmount: Int,
    maxAmount: Int,
    availableBalance: Int,
    focusRequester: FocusRequester
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = stringResource(R.string.withdraw_amount),
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 金额输入框
            OutlinedTextField(
                value = withdrawAmountText,
                onValueChange = { newValue ->
                    // 只允许输入数字和小数点
                    if (newValue.text.isEmpty()) {
                        onTextChange(TextFieldValue("", TextRange.Zero))
                    } else if (newValue.text.matches(Regex("^\\d*\\.?\\d*$"))) {
                        onTextChange(newValue)
                    }
                },
                placeholder = {
                    Text(
                        text = stringResource(R.string.enter_amount),
                        style = MaterialTheme.typography.titleLarge,
                        fontSize = 16.sp,
                        color = Color(0xFF666666)
                    )
                },
                leadingIcon = {
                    Image(
                        painter = painterResource(R.mipmap.ic_coin),
                        contentDescription = "钻石",
                        modifier = Modifier.size(20.dp)
                    )
                },
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                textStyle = MaterialTheme.typography.titleLarge,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Primary,
                    unfocusedTextColor = Color.White,
                    focusedBorderColor = Color(0xFF9F2AF8),
                    unfocusedBorderColor = Color(0xFF363948),
                    focusedContainerColor = Color(0xFF363948),
                    unfocusedContainerColor = Color(0xFF363948),
                    cursorColor = Color(0xFF9F2AF8)
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .focusRequester(focusRequester)
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 快捷金额按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val quickAmounts = listOf(100, 500, 1000, availableBalance)
                quickAmounts.forEachIndexed { index, amount ->
                    val isSelected = withdrawAmount == amount
                    TextButton(
                        onClick = {
                            onAmountChange(amount)
                            focusRequester.requestFocus()
                        }, modifier = Modifier
                            .weight(1f)
                            .then(
                                if (isSelected) {
                                    Modifier.background(
                                        color = Color.Transparent, shape = RoundedCornerShape(8.dp)
                                    )
                                } else {
                                    Modifier
                                }
                            ), colors = ButtonDefaults.buttonColors(
                            containerColor = if (isSelected) Color.Transparent else Color(0xFF363948)
                        ), shape = RoundedCornerShape(8.dp), border = if (isSelected) {
                            BorderStroke(
                                width = 2.dp, color = Primary
                            )
                        } else null
                    ) {
                        Text(
                            text = if (index > 0 && amount == availableBalance) "All" else "$amount",
                            color = if (isSelected) Primary else Color.White,
                            fontWeight = FontWeight.Bold,
                            softWrap = false,
                            style = MaterialTheme.typography.bodyMedium,
                            fontSize = 14.sp
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 限制提示
            Text(
                text = "Min: ${minAmount} • Max: ${maxAmount}",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF999999)
            )
        }
    }
}

/**
 * 费用信息卡片
 */
@Composable
private fun FeeInfoCard(
    withdrawAmount: Int,
    feeRate: Float,
    fee: Int,
    feeType: Int,
    currencyList: List<WalletCurrencyBean> = emptyList(),
    currentCurrency: String = "USD"
) {
    val currentCurrencyData = currencyList.find { it.currency == currentCurrency }
    val convertedAmount = currentCurrencyData?.let {
        (withdrawAmount * it.to_currency_money).toInt()
    } ?: 0

    val withdrawFee = when (feeType) {
        1 -> {//固定金额
            fee.toFloat() * 100
        }

        2 -> {//按比例收费
            feeRate * convertedAmount
        }

        else -> {
            0F
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = stringResource(R.string.transaction_details),
                style = MaterialTheme.typography.titleMedium,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 提现金额
            Row(
                modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.withdraw_amount),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White.copy(alpha = 0.8f)
                )

                Text(
                    text = " ${
                        Constants.Currency.getSymbol(
                            currentCurrency
                        )
                    }${convertedAmount.toShowDiamond()}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 手续费
            Row(
                modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.transaction_fee),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White.copy(alpha = 0.8f)
                )
                Text(
                    text = " ${
                        Constants.Currency.getSymbol(
                            currentCurrency
                        )
                    }${withdrawFee.toInt().toShowDiamond()}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 分割线
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color(0xFF363948))
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 实际到账
            Row(
                modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.you_will_receive),
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
//                    Text(
//                        text = "${currentCurrency}${(convertedAmount - withdrawFee).roundToInt()}",
//                        style = MaterialTheme.typography.bodyMedium,
//                        color = Color(0xFF9F2AF8),
//                        fontWeight = FontWeight.Bold
//                    )

                    // 货币转换显示
                    val currentCurrencyData = currencyList.find { it.currency == currentCurrency }
                    val convertedReceiveAmount = currentCurrencyData?.let {
                        ((withdrawAmount - withdrawFee).coerceAtLeast(0.0F) * it.to_currency_money).toInt()
                    } ?: 0

                    if (convertedReceiveAmount >= 0) {
                        Text(
                            text = " ${
                                Constants.Currency.getSymbol(
                                    currentCurrency
                                )
                            }${ceil((convertedAmount - withdrawFee) / 100).toInt()}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFF9F2AF8),
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 提现须知
 */
@Composable
private fun WithdrawNotice(withdrawRule: String) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "info",
                    tint = Color(0xFF9F2AF8),
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = stringResource(R.string.withdraw_rule),
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = withdrawRule?.replace("\\n","\n").toString(),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f),
                lineHeight = 20.sp
            )

//            val notices = listOf(
//                "Processing time: 1-3 business days",
//                "Minimum withdrawal amount: $100",
//                "Maximum withdrawal amount: $5,000 per day",
//                "Transaction fee: $5 per withdrawal",
//                "Withdrawals are processed Monday to Friday"
//            )
//
//            notices.forEach { notice ->
//                Row(
//                    modifier = Modifier.padding(vertical = 4.dp)
//                ) {
//                    Text(
//                        text = "•",
//                        color = Color(0xFF9F2AF8),
//                        modifier = Modifier.padding(end = 8.dp)
//                    )
//                    Text(
//                        text = notice,
//                        style = MaterialTheme.typography.bodyMedium,
//                        color = Color.White.copy(alpha = 0.8f),
//                        lineHeight = 20.sp
//                    )
//                }
//            }
        }
    }
}


/**
 * 验证提现金额是否有效
 */
private fun isValidAmount(
    amount: Int, minAmount: Int, maxAmount: Int, availableBalance: Int
): Boolean {

    return amount >= minAmount && amount <= maxAmount && amount <= availableBalance
}

@Preview
@Composable
fun WithdrawScreenPreview() {
    AnchorTheme {
        WithdrawScreen(rememberNavController())
    }
}
