package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.RevenueBean
import com.mobile.anchor.app.data.model.SettlementBean
import com.mobile.anchor.app.extension.formatDateTime
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.activities.BankBindActivity
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTabRow
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Background
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.viewmodels.WalletViewModel
import com.mobile.anchor.app.utils.Constants
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * 钱包页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun WalletScreen(
    navController: NavController = rememberNavController(), viewModel: WalletViewModel = viewModel()
) {
    val context = LocalContext.current
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val viewState by viewModel.viewState.observeAsState(PageState.Default)
    val walletInfo by viewModel.walletInfo.collectAsState()

    // 观察ViewModel状态
    val revenueList by viewModel.revenueList.collectAsState()
    val settlementList by viewModel.settlementList.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val selectedPeriod by viewModel.selectedPeriod.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.getWalletInfo()
        viewModel.loadRevenueList(isRefresh = true)
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(context.getString(R.string.my_wallet))
        }) { paddingValues ->

        // 使用统一的 LazyColumn 实现整页滑动和吸顶效果
        WalletContent(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            walletInfo = walletInfo,
            selectedTabIndex = selectedTabIndex,
            selectedPeriod = selectedPeriod,
            revenueList = revenueList,
            settlementList = settlementList,
            isRefreshing = isRefreshing,
            isLoadingMore = isLoadingMore,
            viewState = viewState,
            onTabSelected = { newIndex ->
                selectedTabIndex = newIndex
                viewModel.loadDataForTab(newIndex)
            },
            onWithdrawClick = {
                navController.navigate(Screen.Withdraw.route)
            },
            onBindAccountClick = {
                context.jump(BankBindActivity::class.java)
            },
            onPeriodChange = { period ->
                viewModel.switchPeriod(period)
            },
            onRefresh = {
                if (selectedTabIndex == 0) {
                    viewModel.refreshRevenueList()
                } else {
                    viewModel.refreshSettlementList()
                }
            },
            onLoadMore = {
                if (selectedTabIndex == 0) {
                    viewModel.loadMoreRevenue()
                } else {
                    viewModel.loadMoreSettlement()
                }
            })
    }
}

/**
 * 钱包内容 - 使用 LazyColumn 实现整页滑动和吸顶效果
 */
@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WalletContent(
    modifier: Modifier = Modifier,
    walletInfo: com.mobile.anchor.app.data.model.WalletBean?,
    selectedTabIndex: Int,
    selectedPeriod: Int,
    revenueList: List<RevenueBean>,
    settlementList: List<SettlementBean>,
    isRefreshing: Boolean,
    isLoadingMore: Boolean,
    viewState: PageState,
    onTabSelected: (Int) -> Unit,
    onWithdrawClick: () -> Unit,
    onBindAccountClick: () -> Unit,
    onPeriodChange: (Int) -> Unit,
    onRefresh: () -> Unit,
    onLoadMore: () -> Unit
) {
    val listState = rememberLazyListState()
    val pullRefreshState = rememberPullRefreshState(isRefreshing, onRefresh)

    val currentList = if (selectedTabIndex == 0) revenueList else settlementList
    LaunchedEffect(listState, selectedTabIndex, currentList) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }.distinctUntilChanged()
            .collectLatest { index ->
                if (index != null && index >= currentList.size - 3 && !isLoadingMore && currentList.size >= Constants.GLOBAL_PAGE_SIZE && currentList.size % Constants.GLOBAL_PAGE_SIZE == 0) {
                    onLoadMore()
                }
            }
    }

    Box(
        modifier = modifier.pullRefresh(pullRefreshState)
    ) {
        LazyColumn(
            state = listState, modifier = Modifier.fillMaxSize()
        ) {
            // 余额卡片
            item {
                BalanceCard(
                    balance = walletInfo?.balance ?: 0, onWithdrawClick = onWithdrawClick
                )
                Spacer(modifier = Modifier.height(16.dp))
            }

            // 提现账户行
            item {
                WithdrawalAccountRow(
                    onBindAccountClick = onBindAccountClick
                )
                Spacer(modifier = Modifier.height(10.dp))
            }

            // 吸顶的标签页 - 使用 stickyHeader
            stickyHeader {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Background)
                ) {
                    AnchorTabRow(
                        tabs = listOf(
                            stringResource(R.string.balance), stringResource(R.string.settlement)
                        ),
                        backgroundColor = Color.Transparent,
                        selectedTabIndex = selectedTabIndex,
                        onTabSelected = onTabSelected,
                    )

                    // 如果是 Balance 标签页，显示时间周期选择按钮
                    if (selectedTabIndex == 0) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            // last 7 days 按钮
                            Button(
                                onClick = { onPeriodChange(7) },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (selectedPeriod == 7) Color(0xFF9F2AF8) else Color(
                                        0xFF2A2B35
                                    )
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.last_7_days),
                                    color = if (selectedPeriod == 7) Color.White else Color.White.copy(
                                        alpha = 0.7f
                                    ),
                                    fontSize = 14.sp
                                )
                            }

                            // last 30 days 按钮
                            Button(
                                onClick = { onPeriodChange(30) },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (selectedPeriod == 30) Color(0xFF9F2AF8) else Color(
                                        0xFF2A2B35
                                    )
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.last_30_days),
                                    color = if (selectedPeriod == 30) Color.White else Color.White.copy(
                                        alpha = 0.7f
                                    ),
                                    fontSize = 14.sp
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    } else {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }

            // 标签页内容
            when (selectedTabIndex) {
                0 -> {
                    // Balance 内容
                    balanceListContent(
                        revenueList = revenueList,
                        isLoadingMore = isLoadingMore,
                        viewState = viewState,
                        isRefreshing = isRefreshing,
                        onRefresh = onRefresh
                    )
                }

                1 -> {
                    // Settlement 内容
                    settlementListContent(
                        settlementList = settlementList,
                        isLoadingMore = isLoadingMore,
                        viewState = viewState,
                        isRefreshing = isRefreshing,
                        onRefresh = onRefresh
                    )
                }
            }
        }

        // 下拉刷新指示器
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

/**
 * 余额卡片
 */
@Composable
fun BalanceCard(
    balance: Int, onWithdrawClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color(0xFF9F2AF8), Color(0xFFB347F5)
                        )
                    )
                )
                .padding(16.dp), contentAlignment = Alignment.Center
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    horizontalAlignment = Alignment.Start
                ) {
                    // Balance 标题和货币
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.balance),
                            style = MaterialTheme.typography.titleMedium,
                            color = Color.White.copy(alpha = 0.9f),
                            fontSize = 16.sp
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 余额金额
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "${balance.toShowDiamond()}",
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            fontSize = 28.sp
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Image(
                            painter = painterResource(R.mipmap.ic_coin),
                            contentDescription = "钻石",
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                // 提现按钮
                Button(
                    onClick = onWithdrawClick, colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White.copy(alpha = 0.2f)
                    ), shape = RoundedCornerShape(20.dp), modifier = Modifier.height(40.dp)
                ) {
                    Text(
                        text = stringResource(R.string.withdraw),
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * Balance 列表内容 - LazyColumn 扩展
 */
private fun LazyListScope.balanceListContent(
    revenueList: List<RevenueBean>,
    isLoadingMore: Boolean,
    viewState: PageState,
    isRefreshing: Boolean,
    onRefresh: () -> Unit
) {
    when {
        // 显示状态视图（加载中、错误、空状态）
        revenueList.isEmpty() -> {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp), // 给一个固定高度，避免布局问题
                    contentAlignment = Alignment.Center
                ) {
                    StateView(
                        state = if (isRefreshing) PageState.Loading else viewState,
                        loadingMessage = stringResource(R.string.loading_revenue_records),
                        emptyTitle = stringResource(R.string.no_revenue_records),
                        emptyMessage = stringResource(R.string.revenue_records_will_appear_here),
                        errorTitle = stringResource(R.string.load_failed),
                        errorMessage = stringResource(R.string.failed_to_load_revenue_records),
                        onRetry = onRefresh
                    )
                }
            }
        }
        // 正常列表状态
        else -> {
            LogX.d("收入记录数量：${revenueList.size}")
            items(revenueList) { revenue ->
                RevenueItem(
                    revenue = revenue,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }

            // 加载更多指示器
            if (isLoadingMore) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.loading_more),
                            color = Color.White.copy(alpha = 0.6f),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * Settlement 列表内容 - LazyColumn 扩展
 */
private fun LazyListScope.settlementListContent(
    settlementList: List<SettlementBean>,
    isLoadingMore: Boolean,
    viewState: PageState,
    isRefreshing: Boolean,
    onRefresh: () -> Unit
) {
    when {
        // 显示状态视图（加载中、错误、空状态）
        settlementList.isEmpty() -> {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp), // 给一个固定高度，避免布局问题
                    contentAlignment = Alignment.Center
                ) {
                    StateView(
                        state = if (isRefreshing) PageState.Loading else viewState,
                        loadingMessage = stringResource(R.string.loading_settlement_records),
                        emptyTitle = stringResource(R.string.no_settlement_records),
                        emptyMessage = stringResource(R.string.settlement_records_will_appear_here),
                        errorTitle = stringResource(R.string.load_failed),
                        errorMessage = stringResource(R.string.failed_to_load_settlement_records),
                        onRetry = onRefresh
                    )
                }
            }
        }
        // 正常列表状态
        else -> {
            items(settlementList) { settlement ->
                SettlementItem(
                    settlement = settlement,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }

            // 加载更多指示器
            if (isLoadingMore) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.loading_more),
                            color = Color.White.copy(alpha = 0.6f),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 提现账户行
 */
@Composable
private fun WithdrawalAccountRow(
    onBindAccountClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clickable { onBindAccountClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 钱包图标
            Image(
                painter = painterResource(R.mipmap.ic_withdraw_account),
                contentDescription = "withdrawal account",
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 文本
            Text(
                text = stringResource(R.string.withdrawal_account),
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White,
                modifier = Modifier.weight(1f)
            )

            // Bound 文本
            Text(
                text = stringResource(R.string.bound),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 箭头图标
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "arrow",
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}


/**
 * 收入记录项
 */
@Composable
private fun RevenueItem(
    revenue: RevenueBean, modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 收入类型图标
            Image(
                painter = painterResource(R.mipmap.ic_coin),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 收入信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = revenue.text,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(4.dp))

                Row {
//                    Text(
//                        text = revenue.getFormattedDuration(),
//                        color = Color.White.copy(alpha = 0.5f),
//                        fontSize = 12.sp
//                    )
//                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = revenue.actionAt.formatDateTime(),
                        color = Color.White.copy(alpha = 0.5f),
                        fontSize = 12.sp
                    )
                }
            }
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.End
            ) {
                if (revenue.callDurationSecond > 0) {
                    Text(
                        text = revenue.getFormattedDuration(),
                        color = Color.White.copy(alpha = 0.5f),
                        fontSize = 12.sp
                    )
                }
                // 收入金额
                Text(
                    text = "${if (revenue.coin > 0) "+" else ""}${revenue.coin.toShowDiamond()}",
                    color = Primary,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

/**
 * 结算记录项
 */
@Composable
private fun SettlementItem(
    settlement: SettlementBean, modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 结算类型图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = settlement.getStatusColor(), shape = CircleShape
                    ), contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 结算信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = settlement.getSettleTypeDesc(),
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(Modifier.width(5.dp))

                    Text(
                        text = settlement.getSettleStatusDesc(),
                        color = settlement.getStatusColor(),
                        fontSize = 12.sp
                    )
                }

                if (settlement.reason.isNotEmpty()) {
                    Text(
                        text = settlement.reason,
                        color = Color.White.copy(alpha = 0.5f),
                        fontSize = 12.sp
                    )
                }

                Spacer(Modifier.height(5.dp))
                Text(
                    text = settlement.createdAt.formatDateTime(),
                    color = Color.White.copy(alpha = 0.5f),
                    fontSize = 12.sp
                )
            }

            Column(horizontalAlignment = Alignment.End) {
                // 结算金额
                Text(
                    text = "${settlement.currency}${settlement.currency_price.toShowDiamond()}",
                    color = settlement.getStatusColor(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )

                // 结算金额
//                Text(
//                    text = "-${settlement.coin.toShowDiamond()}", color = Primary, fontSize = 14.sp
//                )
            }
        }
    }
}

@Preview
@Composable
fun WalletScreenPreview() {
    AnchorTheme {
        WalletScreen()
    }
}

@Preview
@Composable
fun RevenueItemPreview() {
    AnchorTheme {
        RevenueItem(
            revenue = RevenueBean(
                callId = "1",
                userId = 123,
                userNickName = "Alice",
                userAvatar = "",
                connectAt = "2024-01-15 14:30",
                callDurationSecond = 10,
                coin = 50,
                effect = true
            )
        )
    }
}

@Preview
@Composable
fun SettlementItemPreview() {
    AnchorTheme {
        SettlementItem(
            settlement = SettlementBean(
                id = "1",
                currency_price = 1500,
                reason = "reject",
                stat = 3,
                createdAt = System.currentTimeMillis(),
            )
        )
    }
}