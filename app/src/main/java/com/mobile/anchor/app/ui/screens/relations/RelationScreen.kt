package com.mobile.anchor.app.ui.screens.relations

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.OnlineStatus
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.ui.activities.UserDetailActivity
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTabRow
import com.mobile.anchor.app.ui.components.OnlineStatusText
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.view.LevelLabelView
import io.rong.imlib.model.Conversation
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

/**
 * 好友页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RelationScreen(
    viewModel: RelationViewModel = viewModel()
) {
    val context = LocalContext.current
    val tabs = listOf(
        stringResource(R.string.relation_recommend), stringResource(R.string.relation_follow)
    )

    val coroutineScope = rememberCoroutineScope()

    val pagerState = rememberPagerState(initialPage = 0, pageCount = { tabs.size })

    AnchorScaffold(
        topBar = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(top = dimensionResource(R.dimen.dp_32))
                    .height(dimensionResource(R.dimen.dp_40))
            ) {
                AnchorTabRow(
                    modifier = Modifier.weight(1F),
                    tabs = tabs,
                    backgroundColor = Color.Transparent,
                    selectedTextSize = dimensionResource(R.dimen.sp_18).value.sp,
                    unselectedTextSize = dimensionResource(R.dimen.sp_18).value.sp,
                    selectedTabIndex = pagerState.currentPage,
                    onTabSelected = { index ->
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(index)
                        }
                    })
                Spacer(Modifier.width(10.dp))
            }
        }) { paddingValues ->
        Column(modifier = Modifier.padding(top = paddingValues.calculateTopPadding())) {
            HorizontalPager(
                state = pagerState
            ) { page ->
                // 根据当前标签页显示对应的内容
                RelationTabContent(
                    tab = page, viewModel = viewModel, onNavigateToUserDetail = {
                        context.jump(UserDetailActivity::class.java, Bundle().apply {
                            putString("id", it)
                        })
                    })
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RelationTabContent(
    tab: Int, viewModel: RelationViewModel, onNavigateToUserDetail: (String) -> Unit = {}
) {
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val viewState by viewModel.viewState.observeAsState(PageState.Default)

    val gridState = rememberLazyListState()

    // 获取当前标签的好友列表
    val userList by viewModel.getRelationList(tab).collectAsState()

    // 首次加载数据
    LaunchedEffect(tab) {
        if (userList.isEmpty()) {
            viewModel.loadRelationList(tab, isRefresh = true)
        }
    }

    // 监听滑动到底部自动加载更多
    LaunchedEffect(gridState, userList.size) {
        snapshotFlow { gridState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }.distinctUntilChanged()
            .collectLatest { index ->
                if (index != null && index >= userList.size - 3 && !isLoadingMore && viewModel.canLoadMore(
                        tab
                    )
                ) {
                    viewModel.loadMoreRelations(tab)
                }
            }
    }

    // 官方 pullRefresh 处理
    val refreshState = rememberPullRefreshState(
        refreshing = isRefreshing, onRefresh = {
            viewModel.refreshRelationList(tab)
        })

    Box(
        modifier = Modifier
            .fillMaxSize()
            .pullRefresh(refreshState)
    ) {
        when {
            // 显示状态视图（加载中、错误、空状态）
            userList.isEmpty() -> {
                StateView(
                    state = if (isRefreshing) PageState.Loading else viewState,
                    loadingMessage = stringResource(
                        R.string.loading_users, stringResource(R.string.relation_recommend)
                    ),
                    emptyTitle = when (tab) {
                        0 -> stringResource(R.string.no_recommendations)
                        1 -> stringResource(R.string.no_following_users)
                        else -> stringResource(R.string.data_will_appear_here)
                    },
                    emptyMessage = when (tab) {
                        0 -> stringResource(R.string.recommended_users_will_appear_here)
                        1 -> stringResource(R.string.users_you_follow_will_appear_here)
                        else -> stringResource(R.string.data_will_appear_here)
                    },
                    errorTitle = stringResource(R.string.load_failed),
                    errorMessage = stringResource(
                        R.string.failed_to_load_users, stringResource(R.string.relation_follow)
                    ),
                    onRetry = {
                        viewModel.refreshRelationList(tab)
                    })
            }
            // 正常列表状态
            else -> {
                LazyColumn(
                    state = gridState,
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(userList.size) { index ->
                        RelationItem(user = userList[index], onClick = {
                            onNavigateToUserDetail(userList[index].id)
                        })
                    }

                    // 加载更多指示器
                    if (isLoadingMore) {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }
                    }
                }
            }
        }

        // 下拉刷新指示器
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = refreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@Composable
private fun RelationItem(
    user: UserBean, onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable {
                onClick()
            }, verticalAlignment = Alignment.CenterVertically
    ) {
        Box {
            // 头像
            AsyncImage(
                model = user.avatar.buildImageUrl(thumbnail = true),
                contentDescription = "Avatar",
                placeholder = painterResource(id = R.mipmap.ic_default_avatar),
                error = painterResource(id = R.mipmap.ic_default_avatar),
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 用户信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row {
                Text(
                    text = user.nickname,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                OnlineStatusText(
                    OnlineStatus.fromValue(user.status), modifier = Modifier.padding(start = 4.dp)
                )
            }

            Row(
                modifier = Modifier.padding(top = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AndroidView(factory = { ctx ->
                    LevelLabelView(ctx)
                }, update = {
                    it.current = user.userLevelConfig?.level ?: 1
                })
                Text(
                    user.userCountry?.title ?: "",
                    style = MaterialTheme.typography.labelSmall,
                    color = Color.White,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .background(
                            color = Color(0xff422574), shape = RoundedCornerShape(50)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }

        val context = LocalContext.current
        Image(
            painter = painterResource(R.mipmap.ic_anchor_message),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .size(44.dp)
                .clickable {
                    context.jump(MyRongConversationActivity::class.java, Bundle().apply {
                        putString("targetId", user.id)
                        putString(
                            "ConversationType", Conversation.ConversationType.PRIVATE.name
                        )
                    })
                })
    }
}


@Preview
@Composable
fun FriendScreenPreview() {
    AnchorTheme {
        Column {
//            FriendItem(
//                friend = FriendResponse(
//                    id = "1",
//                    name = "Test User",
//                    avatar = null,
//                    status = "online"
//                )
//            ) { }
        }
    }
}
