package com.mobile.anchor.app.ui.screens.home

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.DashboardBean
import com.mobile.anchor.app.extension.formatAsPercent
import com.mobile.anchor.app.extension.formatCountdown
import com.mobile.anchor.app.extension.toMinutes
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.lifecycle.AppLifecycleObserver
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.activities.LevelActivity
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.CardView
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.LoadingDialog
import com.mobile.anchor.app.ui.components.MarqueeText
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import com.mobile.anchor.app.ui.fragments.HomeFragment
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.utils.CameraPreviewHelper
import com.mobile.anchor.app.utils.CameraPreviewHelper.CameraView
import com.mobile.anchor.app.utils.Constants
import io.rong.imlib.model.Conversation

/**
 * 首页界面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun HomeScreen() {
    val context = LocalContext.current
    val viewModel: HomeViewModel = viewModel()
    val lifecycleOwner = LocalLifecycleOwner.current
    val activity = LocalActivity.current
    val uiState by viewModel.uiState.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    var showLoadingDialog by remember { mutableStateOf(false) }
    var showCameraPreview by remember { mutableStateOf(false) }
    var cameraPermissionLauncher: ActivityResultLauncher<String>? = null

    fun stopPreview() {
        showCameraPreview = false
        CameraPreviewHelper.stopCameraPreview()
    }

    fun startPreview() {
        if (uiState.workbenchBean.working_mod && !showCameraPreview) {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.CAMERA
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                showCameraPreview = true
                Handler(Looper.getMainLooper()).postDelayed({
                    CameraPreviewHelper.startCameraPreview(context)
                }, 150)
            } else {
                cameraPermissionLauncher?.launch(Manifest.permission.CAMERA)
            }
        } else {
            stopPreview()
        }
    }

    // 权限请求
    cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startPreview()
        }
    }

    // 弹窗确认状态
    var showConfirmDialog by remember { mutableStateOf(false) }
    var pendingWorkModeStatus by remember { mutableStateOf(false) }

    // 说明弹框状态
    var showInfoDialog by remember { mutableStateOf(false) }
    var infoDialogTitle by remember { mutableStateOf("") }
    var infoDialogContent by remember { mutableStateOf("") }

    LaunchedEffect(Unit) {
        // 初始化摄像头
        CameraPreviewHelper.setupCamera(context)
        // 检测冷启动并自动开启工作模式
        val isColdStart = AppLifecycleObserver.getInstance().checkAndMarkColdStart()
        if (isColdStart && DataStoreManager.getUserObject()?.isVerified == true) {
            viewModel.autoEnableWorkMode()
        } else {
            LogX.d("从后台恢复，不自动开启工作模式")
        }

        activity?.let { it as MainActivity }?.tabChangedFlow?.flowWithLifecycle(
            lifecycleOwner.lifecycle, Lifecycle.State.STARTED
        )?.collect { fragment ->
            if (fragment is HomeFragment) {
                startPreview()
            } else {
                stopPreview()
            }
        }
    }

    // 监听在线状态变化
    LaunchedEffect(uiState.workbenchBean.working_mod) {
        pendingWorkModeStatus = uiState.workbenchBean.working_mod
        showLoadingDialog = false
        startPreview()
    }

    //监听页面生命周期，管理摄像头预览
    DisposableEffect(lifecycleOwner) {
        val observer = androidx.lifecycle.LifecycleEventObserver { _, event ->
            when (event) {
                // 页面恢复时，如果在线状态为开启，则启动摄像头预览
                Lifecycle.Event.ON_RESUME -> {
                    val fragment =
                        activity?.let { it as AppCompatActivity }?.supportFragmentManager?.fragments?.firstOrNull { it.isVisible }
                    if (fragment is HomeFragment) {
                        startPreview()
                    }
                }

                // 页面暂停时停止预览
                Lifecycle.Event.ON_PAUSE -> {
                    // 页面暂停时停止预览
                    stopPreview()
                }

                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            // 清理摄像头资源
            stopPreview()
        }
    }

    // 摄像头预览作为背景
    Box(modifier = Modifier.fillMaxSize()) {
        // 摄像头预览背景
        if (showCameraPreview) {
            CameraView()
        }

        // 下拉刷新状态
        val pullRefreshState = rememberPullRefreshState(
            refreshing = isRefreshing, onRefresh = {
                viewModel.loadAllData()
            })

        // UI内容层，当摄像头预览开启时透明度为50%
        AnchorScaffold(
            modifier = Modifier.alpha(if (showCameraPreview) 0.5f else 1f), topBar = {
                AnchorTopBar(stringResource(id = R.string.home_title), showNavigationIcon = false, modifier = Modifier.padding(top = dimensionResource(R.dimen.dp_20)))
            }) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = paddingValues.calculateTopPadding())
                    .pullRefresh(pullRefreshState)
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(15.dp)
                ) {
//                    item {
//                        TaskCard(uiState.workbenchBean) { title, content ->
//                            showInfoDialog = true
//                            infoDialogTitle = title
//                            infoDialogContent = content
//                        }
//                    }

                    item {
                        SwitchCard(
                            uiState = uiState, onShowConfirmDialog = { status ->
                                pendingWorkModeStatus = status
                                showConfirmDialog = true
                            })
                    }

                    // 跑马灯
                    item {
                        uiState.workbenchBean.tips_list?.filter { it.isNotEmpty() && it.isNotBlank() }
                            ?.takeIf { it.isNotEmpty() }?.let {
                                MarqueeCard(it)
                            }
                    }

                    item {
                        Workboard(
                            workbenchBean = uiState.workbenchBean,
                            onShowInfoDialog = { title, content ->
                                infoDialogTitle = title
                                infoDialogContent = content
                                showInfoDialog = true
                            },
                            onNavigateToNotification = {
                                context.jump(
                                    MyRongConversationActivity::class.java, Bundle().apply {
                                        putString("targetId", Constants.RONG_YUN_ID_SYSTEM)
                                        putString(
                                            "ConversationType",
                                            Conversation.ConversationType.SYSTEM.name
                                        )
                                    })
                            },
                            onNavigateToLevel = { context.jump(LevelActivity::class.java) })
                        Spacer(Modifier.height(15.dp))
                    }
                }

                // 下拉刷新指示器
                PullRefreshIndicator(
                    refreshing = isRefreshing,
                    state = pullRefreshState,
                    modifier = Modifier.align(Alignment.TopCenter)
                )
            }
        }
    }

    // 确认对话框
    ConfirmDialog(
        visible = showConfirmDialog,
        title = stringResource(R.string.reminder),
        content = if (pendingWorkModeStatus) stringResource(R.string.turn_on_working_tips)
        else stringResource(R.string.turn_off_working_tips),
        confirmText = stringResource(R.string.rc_confirm),
        cancelText = stringResource(R.string.rc_cancel),
        onDismiss = { showConfirmDialog = false },
        onConfirm = {
            if (DataStoreManager.getUserObject()?.isVerified == true) {
                showLoadingDialog = true
                viewModel.updateWorkModeStatus(pendingWorkModeStatus)
            } else {
                ToastUtil.show(context.getString(R.string.unable_to_activate_working_mode))
            }

            showConfirmDialog = false
        },
        onCancel = { showConfirmDialog = false })

    // 说明弹框
    ConfirmDialog(
        visible = showInfoDialog,
        title = infoDialogTitle,
        content = infoDialogContent,
        confirmText = stringResource(R.string.rc_confirm),
        showCancelButton = false, // 只显示确认按钮
        onDismiss = { showInfoDialog = false },
        onConfirm = { showInfoDialog = false })

    LoadingDialog(visible = showLoadingDialog)
}

@Composable
private fun TaskCard(dashboardBean: DashboardBean?, onShowInfoDialog: (String, String) -> Unit) {
    val context = LocalContext.current
    CardView {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable {
                onShowInfoDialog(
                    "", context.getString(
                        R.string.home_instruction_explain,
                        "${dashboardBean?.each_match_effect_coin}",
                        "${dashboardBean?.match_call_effect_duration}",
                        "${dashboardBean?.anchor_cycle_times}"
                    )
                )
            }) {
                Icon(
                    painter = painterResource(R.mipmap.ic_tip),
                    contentDescription = "任务信息",
                    tint = Color.Red,
                    modifier = Modifier.size(15.dp)
                )
                Spacer(Modifier.width(4.dp))
                Text(
                    text = stringResource(
                        R.string.match_bonus_coins,
                        dashboardBean?.anchor_match_coin?.toShowDiamond() ?: 0,
                        dashboardBean?.anchor_match_times_step ?: 0,
                        dashboardBean?.anchor_cycle_times ?: 0
                    ), color = Color.Red
                )
            }

            Spacer(Modifier.height(10.dp))
            Text(
                stringResource(
                    R.string.valid_match_calls_today,
                    dashboardBean?.today_data?.anchor_effect_match_times ?: 0
                ), fontSize = 14.sp, color = Color(0xff666666)
            )
        }
    }
}

@Composable
private fun SwitchCard(
    uiState: HomeUiState, onShowConfirmDialog: (Boolean) -> Unit
) {
    val context = LocalContext.current
    CardView {
        Column(modifier = Modifier.padding(16.dp)) {
            // 在线状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(stringResource(id = R.string.work_mode), color = Color.White)
                Switch(
                    checked = uiState.workbenchBean.working_mod, onCheckedChange = { isChecked ->
                        if (DataStoreManager.getUserObject()?.isVerified == false) {
                            ToastUtil.show(context.getString(R.string.unable_to_activate_working_mode))
                            return@Switch
                        }
                        onShowConfirmDialog(isChecked)
                    }, colors = SwitchDefaults.colors(
                        checkedThumbColor = Primary, checkedTrackColor = Primary.copy(alpha = 0.5f)
                    )
                )
            }
        }
    }
}

/**
 * 通知卡片组件，使用跑马灯效果显示通知
 */
@Composable
private fun MarqueeCard(marqueeList: List<String>) {
    // 通知内容区域
    CardView(
        modifier = Modifier
            .fillMaxWidth()
            .clipToBounds()
    ) {
        MarqueeText(marqueeList)
    }
}

@Composable
private fun Workboard(
    workbenchBean: DashboardBean?,
    onShowInfoDialog: (String, String) -> Unit = { _, _ -> },
    onNavigateToNotification: () -> Unit = {},
    onNavigateToLevel: () -> Unit = {}
) {

    val context = LocalContext.current
    Column {

        // Working time
        CardView {
            Column(
                modifier = Modifier.padding(vertical = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.working_time),
                    color = Color.White,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(Modifier.height(15.dp))
                Row {
                    Column(
                        modifier = Modifier.weight(1F),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            stringResource(
                                R.string.mins,
                                workbenchBean?.today_data?.working_duration?.toMinutes() ?: 0
                            ), color = Color.White, style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(Modifier.height(5.dp))
                        Text(
                            stringResource(R.string.today),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    Column(
                        modifier = Modifier.weight(1F),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            workbenchBean?.week_data?.working_duration?.formatCountdown()
                                ?: "00:00",
                            color = Color.White,
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(Modifier.height(5.dp))
                        Text(
                            stringResource(R.string.week),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Average call duration and Connection Rate
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                ) {
                    Text(
                        stringResource(R.string.average_call_duration),
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        workbenchBean?.week_data?.avg_call_duration?.formatCountdown() ?: "0",
                        color = Color.White,
                        style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(R.string.week),
                        color = Color.Gray,
                        style = MaterialTheme.typography.labelMedium
                    )
                }
            }
            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                ) {
                    Text(
                        stringResource(R.string.connection_rate),
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean?.today_data?.connect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.week_rate_prefix,
                            "${workbenchBean?.week_data?.connect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Match Call Completion Rate
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            onShowInfoDialog(
                                context.getString(R.string.match_call_completion_rate_title),
                                context.getString(
                                    R.string.match_call_completion_rate_tips,
                                    workbenchBean?.match_call_effect_duration
                                )
                            )
                        }
                        .padding(16.dp)) {
                    Text(
                        stringResource(R.string.match_call_completion_rate),
                        color = Color(0xff666666),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean?.today_data?.match_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.week_rate_prefix,
                            "${workbenchBean?.week_data?.match_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            onShowInfoDialog(
                                context.getString(R.string.video_call_completion_rate_title),
                                context.getString(
                                    R.string.video_call_completion_rate_tips,
                                    workbenchBean?.normal_call_effect_duration
                                )
                            )
                        }
                        .padding(16.dp)) {
                    Text(
                        stringResource(R.string.video_call_completion_rate),
                        textAlign = TextAlign.Center,
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean?.today_data?.normal_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.week_rate_prefix,
                            "${workbenchBean?.week_data?.normal_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Notify and Level
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onNavigateToNotification() }
                        .padding(16.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_home_notify),
                        contentDescription = "Icon",
                        modifier = Modifier.size(40.dp)
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(stringResource(R.string.notify), color = Color.White)
                }
            }

            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onNavigateToLevel() }
                        .padding(16.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_home_level),
                        contentDescription = "Icon",
                        modifier = Modifier.size(40.dp)
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(stringResource(R.string.level), color = Color.White)
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    AnchorTheme {
        Column(modifier = Modifier.padding(16.dp)) {
            TaskCard(null) { _, _ -> }
            Spacer(Modifier.height(10.dp))
            SwitchCard(HomeUiState(), {})
//            SwitchCard()
            Spacer(Modifier.height(10.dp))
            Workboard(null)
        }
    }
}
