package com.mobile.anchor.app.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraDevice
import android.hardware.camera2.CameraManager
import android.media.AudioManager
import android.util.Size
import android.view.Surface
import android.view.TextureView
import android.view.WindowManager
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.mobile.anchor.app.logger.LogX
import java.lang.ref.WeakReference
import kotlin.math.abs


/**
 * 摄像头预览助手类
 * 提供摄像头预览的设置、启动、停止等功能
 */
object CameraPreviewHelper {

    private const val TAG = "CameraPreviewHelper"
    private var cameraManager: CameraManager? = null
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null

    private var textureViewRef: WeakReference<TextureView>? = null
    private var previewSize: Size? = null
    private var cameraId: String? = null

    @Composable
    fun CameraView() {
        val context = LocalContext.current
        // 摄像头相关状态
        val cm = cameraManager
            ?: remember { context.getSystemService(Context.CAMERA_SERVICE) as? CameraManager }

        AndroidView(
            modifier = Modifier.fillMaxSize(), factory = { ctx ->
                TextureView(ctx).apply {
                    val textureView = this
                    textureViewRef = WeakReference(textureView)
                    textureView.surfaceTextureListener =
                        object : TextureView.SurfaceTextureListener {
                            override fun onSurfaceTextureAvailable(
                                surface: SurfaceTexture, width: Int, height: Int
                            ) {
                                val sizes = cm?.getCameraCharacteristics(cameraId ?: "")
                                    ?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                                    ?.getOutputSizes(SurfaceTexture::class.java) ?: emptyArray()

                                previewSize = optimizedPreviewSize(
                                    sizes, width, height
                                )

                                // 先应用变换，再启动预览
                                previewSize?.let {
                                    updateTextureViewTransform(this@apply, it)
                                }
                            }

                            override fun onSurfaceTextureSizeChanged(
                                surface: SurfaceTexture, width: Int, height: Int
                            ) {
                                // 当Surface尺寸改变时重新应用变换
                                previewSize?.let {
                                    updateTextureViewTransform(this@apply, it)
                                }
                            }

                            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean =
                                true

                            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
                        }
                }
            })
    }

    /**
     * 设置摄像头
     */
    fun setupCamera(context: Context) {
        try {
            cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            // 遍历摄像头列表，找到前置摄像头
            for (id in cameraManager!!.cameraIdList) {
                val characteristics = cameraManager!!.getCameraCharacteristics(id)
                val facing = characteristics.get(CameraCharacteristics.LENS_FACING)
                if (facing == CameraCharacteristics.LENS_FACING_FRONT) {
                    cameraId = id
                    break
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 启动摄像头预览
     */
    fun startCameraPreview(
        context: Context, callback: ((CameraDevice, CameraCaptureSession) -> Unit)? = null
    ) {
        // 确保摄像头管理器、TextureView、摄像头ID和预览尺寸都已正确设置
        if (cameraManager == null || textureViewRef?.get() == null || cameraId?.isEmpty() == true || previewSize == null) {
            return
        }
        try {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.CAMERA
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }

            cameraManager?.openCamera(cameraId ?: "", object : CameraDevice.StateCallback() {
                override fun onOpened(camera: CameraDevice) {
                    try {
                        val surfaceTexture = textureViewRef?.get()?.surfaceTexture
                        surfaceTexture?.setDefaultBufferSize(
                            previewSize?.width ?: 0, previewSize?.height ?: 0
                        )
                        surfaceTexture?.let {
                            val surface = Surface(it)
                            val previewRequestBuilder =
                                camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
                            previewRequestBuilder.addTarget(surface)

                            camera.createCaptureSession(
                                listOf(surface), object : CameraCaptureSession.StateCallback() {
                                    override fun onConfigured(session: CameraCaptureSession) {
                                        try {
                                            // 确保在开始预览前应用正确的变换
                                            textureViewRef?.get()?.let {
                                                updateTextureViewTransform(
                                                    it, previewSize ?: Size(0, 0)
                                                )
                                                session.setRepeatingRequest(
                                                    previewRequestBuilder.build(), null, null
                                                )
                                                callback?.invoke(camera, session)
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }

                                    override fun onConfigureFailed(session: CameraCaptureSession) {
                                        LogX.e(
                                            TAG,
                                            "Camera configuration failed ${session.isReprocessable}"
                                        )
                                        // 配置失败
                                    }
                                }, null
                            )
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onDisconnected(camera: CameraDevice) {
                    camera.close()
                }

                override fun onError(camera: CameraDevice, error: Int) {
                    camera.close()
                }
            }, null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 停止摄像头预览
     */
    fun stopCameraPreview() {
        try {
            captureSession?.close()
            cameraDevice?.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * [REVISED]
     * 更新TextureView的变换矩阵以实现铺满全屏且不变形的效果（CenterCrop）。
     * 此版本使用了更健壮的矩阵计算方法，能正确处理不同设备的屏幕和传感器朝向。
     */
    private fun updateTextureViewTransform(
        textureView: TextureView, previewSize: Size, isFrontCamera: Boolean = false
    ) {
        val viewWidth = previewSize.height
        val viewHeight = previewSize.width

        if (viewWidth == 0 || viewHeight == 0 || previewSize.width == 0 || previewSize.height == 0) {
            return
        }

        val matrix = Matrix()
        val centerX = viewWidth / 2f
        val centerY = viewHeight / 2f

        val originalPreviewWidth = previewSize.width.toFloat()
        val originalPreviewHeight = previewSize.height.toFloat()

        matrix.reset()

        val windowManager =
            ContextCompat.getSystemService(textureView.context, WindowManager::class.java)
        val displayRotation = windowManager?.defaultDisplay?.rotation ?: Surface.ROTATION_0
        val displayRotationDegrees = getSurfaceOrientationDegrees(displayRotation)

        // 1. 计算原始的旋转补偿值 (用于后续的逻辑判断，如宽高交换和镜像轴)
        val rotationCompensation = (getSensorOrientation(
            textureView.context, isFrontCamera
        ) - displayRotationDegrees + 360) % 360

        // *** 关键修改：根据您的测试结果，对最终显示旋转角度进行校正 ***
        // 假设：当计算出 270 时，实际需要的是 180。这意味着我们需要在计算出的基础上减去 90 度 (或加上 270 度)。
        val finalRotationAngleForDisplay = (rotationCompensation - 90 + 360) % 360

        // 2. 应用旋转：使用校正后的角度，使预览内容在屏幕上正确显示
        matrix.postRotate(finalRotationAngleForDisplay.toFloat(), centerX, centerY)


        // 3. 应用镜像 (仅前置摄像头需要)
        // 镜像轴的选择仍然依赖于原始的 rotationCompensation，因为它反映了图像经过传感器和初步旋转后的真实方向变化。
//    if (isFrontCamera) {
//        if (rotationCompensation == 90 || rotationCompensation == 270) {
//            // 如果原始旋转补偿是 90 或 270 度，意味着图像的横轴已变为垂直方向，所以镜像需要沿着当前垂直轴。
//            matrix.postScale(1f, -1f, centerX, centerY)
//        } else {
//            // 如果原始旋转补偿是 0 或 180 度，意味着图像的横轴仍是水平方向，所以镜像沿着水平轴。
//            matrix.postScale(-1f, 1f, centerX, centerY)
//        }
//    }


        // 4. 计算并应用缩放 - 实现真正的CenterCrop
        // 用于缩放的预览尺寸，也依赖于原始的 rotationCompensation，以判断宽高是否需要交换。
        val rotatedPreviewWidth: Float
        val rotatedPreviewHeight: Float
        if (rotationCompensation == 90 || rotationCompensation == 270) {
            // 如果原始旋转补偿是 90 或 270 度，预览图像的有效宽高需要互换。
            rotatedPreviewWidth = originalPreviewHeight
            rotatedPreviewHeight = originalPreviewWidth
        } else {
            // 如果原始旋转补偿是 0 或 180 度，宽高保持不变。
            rotatedPreviewWidth = originalPreviewWidth
            rotatedPreviewHeight = originalPreviewHeight
        }

        val scaleX = viewWidth / rotatedPreviewWidth
        val scaleY = viewHeight / rotatedPreviewHeight
        val scale = kotlin.math.max(scaleX, scaleY)

        matrix.postScale(scale, scale, centerX, centerY)

        // 5. 调整平移，确保最终图像在 TextureView 中居中
        val finalScaledWidth = rotatedPreviewWidth * scale
        val finalScaledHeight = rotatedPreviewHeight * scale
        val dx = (viewWidth - finalScaledWidth) / 2f
        val dy = (viewHeight - finalScaledHeight) / 2f
        matrix.postTranslate(dx, dy)

        textureView.setTransform(matrix)
    }

    private fun getSensorOrientation(context: Context, isFrontCamera: Boolean): Int {
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        var sensorOrientation = 0 // 默认值

        try {
            // 遍历所有可用的摄像头ID
            for (cameraId in cameraManager.cameraIdList) {
                val characteristics = cameraManager.getCameraCharacteristics(cameraId)

                // 获取摄像头朝向 (LENS_FACING)
                val cameraFacing = characteristics.get(CameraCharacteristics.LENS_FACING)

                // 判断是否是前置摄像头或后置摄像头
                if (isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_FRONT) {
                    // 找到了前置摄像头
                    sensorOrientation =
                        characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                    break // 找到后即可退出循环
                } else if (!isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_BACK) {
                    // 找到了后置摄像头
                    sensorOrientation =
                        characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                    break // 找到后即可退出循环
                }
            }
        } catch (e: Exception) {
            LogX.e("CameraInfo", "Error getting camera characteristics: ${e.message}")
        }
        return sensorOrientation
    }

    private fun getSurfaceOrientationDegrees(rotation: Int): Int {
        return when (rotation) {
            Surface.ROTATION_0 -> 0
            Surface.ROTATION_90 -> 90
            Surface.ROTATION_180 -> 180
            Surface.ROTATION_270 -> 270
            else -> 0
        }
    }

    /**
     * 选择与目标宽高比最接近的预览尺寸
     */
    fun closestAspectRatioSize(
        availableSizes: Array<Size>,
        surfaceWidth: Int,
        surfaceHeight: Int,
        maxAspectDistortion: Float = 0.05f // 比例误差最大容忍值
    ): Size? {
        if (availableSizes.isEmpty() || surfaceWidth == 0 || surfaceHeight == 0) {
            return null
        }

        // 确定目标宽高比。
        // 预览尺寸通常是横向的 (width > height)，而我们的视图是竖向的。
        // 为了正确匹配，我们将视图的宽高比也计算为 (长边 / 短边)。
        val targetRatio = if (surfaceWidth > surfaceHeight) {
            surfaceWidth.toFloat() / surfaceHeight.toFloat()
        } else {
            surfaceHeight.toFloat() / surfaceWidth.toFloat()
        }

        LogX.d(
            "CameraPreviewSize",
            "Target aspect ratio: $targetRatio (from view size ${surfaceWidth}x${surfaceHeight})"
        )

        // 直接使用 minByOrNull 找到与目标宽高比差异最小的尺寸。
        val bestSize = availableSizes.minByOrNull { size ->
            val ratio = size.width.toFloat() / size.height.toFloat()
            abs(ratio - targetRatio)
        }

        LogX.d(
            "CameraPreviewSize",
            "Best matching size: ${bestSize?.width}x${bestSize?.height} (Ratio: ${bestSize?.let { it.width.toFloat() / it.height.toFloat() }})"
        )

        return bestSize
    }

    /**
     * 选择合适的预览尺寸，确保比例匹配且宽度不超过最大限制
     */
    fun optimizedPreviewSize(
        availableSizes: Array<Size>, surfaceWidth: Int, surfaceHeight: Int, maxWidth: Int = 3000
    ): Size? {
        val targetRatio = surfaceWidth.toFloat() / surfaceHeight.toFloat()

        val matchingSizes = availableSizes.filter {
            val ratio = it.height.toFloat() / it.width
            ratio == targetRatio
        }

        val selected = matchingSizes.filter { it.width >= maxWidth }.minByOrNull { it.width }

        return selected ?: matchingSizes.lastOrNull() ?: closestAspectRatioSize(
            availableSizes, surfaceWidth, surfaceHeight
        )
    }
}
