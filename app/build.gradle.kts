import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.google.gradle.ksp)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
    id("kotlin-parcelize")
}

val keystorePropertiesFile = rootProject.file("gradle.properties")
val keystoreProperties = Properties()
keystoreProperties.load(keystorePropertiesFile.inputStream())

android {
    namespace = "com.mobile.anchor.app"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.mobile.anchor.app"
        minSdk = 24
        targetSdk = 36
        versionCode = 20
        versionName = "2.0.1"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters.addAll(listOf("arm64-v8a", "armeabi-v7a"))
        }

    }

    signingConfigs {
        create("release") {
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
        }
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
            buildConfigField("String", "HOST", "\"https://api.yocovip.com\"")
            buildConfigField(
                "String", "SOCKET_HOST", "\"wss://api.yocovip.com/api/v1/socket/ws\""
            )
            buildConfigField(
                "String",
                "BUILD_TIME",
                "\"${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))}\""
            )
        }
        create("uat") {
            initWith(getByName("release")) // 复用 release 配置
            matchingFallbacks.add("release")
            versionNameSuffix = ".uat"
            buildConfigField("String", "HOST", "\"https://api.yocovip.com\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.yocovip.com/api/v1/socket/ws\"")
        }
        getByName("debug") {
            isMinifyEnabled = false
            isDebuggable = true
            versionNameSuffix = ".test"
            signingConfig = signingConfigs.getByName("release")
//            buildConfigField("String", "HOST", "\"https://api.yocovip.com\"")
//            buildConfigField("String", "SOCKET_HOST", "\"wss://api.yocovip.com/api/v1/socket/ws\"")
            buildConfigField("String", "HOST", "\"https://api.idim888.com\"")
            buildConfigField("String", "SOCKET_HOST", "\"wss://api.idim888.com/api/v1/socket/ws\"")
            buildConfigField(
                "String",
                "BUILD_TIME",
                "\"${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))}\""
            )
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
//    kotlinOptions {
//        jvmTarget = "17"
//    }
    kotlin {
        jvmToolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }
    buildFeatures {
        compose = true
        viewBinding = true
        buildConfig = true
    }

    applicationVariants.all {
        outputs.all {
            (this as com.android.build.gradle.internal.api.BaseVariantOutputImpl).outputFile
            val dateFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
            val currentDate = LocalDateTime.now().format(dateFormat)
            val apkName =
                "anchor-${buildType.name}-${defaultConfig.versionName}-${defaultConfig.versionCode}-$currentDate.apk"
            outputFileName = apkName
        }
    }
}

dependencies {
    implementation(project(":library"))

    // Core Android
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)

    // AppCompat and Material
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.fragment.ktx)

    // Compose
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.constraintlayout.compose)
    implementation(libs.androidx.foundation)
    implementation(libs.androidx.material) // pullRefresh
    implementation(libs.splashscreen)
    implementation(libs.reorderable)

    // Navigation
    implementation(libs.androidx.navigation.compose)

    // ViewModel
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    implementation(libs.androidx.runtime.livedata)

    // Network
    implementation(libs.retrofit)
    implementation(libs.converter.gson)
    implementation(libs.retrofit.kotlinx.serialization)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.squareup.moshi)
    implementation(libs.squareup.moshiKt)
    implementation(libs.camera.view)
    ksp(libs.squareup.moshiCodegen)
    implementation(libs.retrofitMoshi)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.auth.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.play.services.auth)
    implementation(libs.firebase.crashlytics.ktx)

    // Image Loading
    implementation(libs.coil.compose)

    // CameraX
    implementation(libs.androidx.camera.core)
    implementation(libs.androidx.camera.camera2)
    implementation(libs.androidx.camera.lifecycle)
    implementation(libs.androidx.camera.video)
    implementation(libs.androidx.camera.view)
    implementation(libs.androidx.camera.extensions)

    // Coroutines
    implementation(libs.kotlinx.coroutines.android)

    // DataStore
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.core)
    implementation(libs.androidx.runtime)
    implementation(libs.gson)
    implementation(libs.logger)
    implementation(libs.rongYun)
    implementation(libs.androidx.preference)
    implementation(libs.lifecycleProcess)
    implementation(libs.rtcFull)

    implementation(libs.svgaplayer.android)
    implementation(libs.xxPermission)

    implementation(libs.androidx.emoji2)

    // Google Sign-In
    implementation(libs.play.services.auth)
    implementation(libs.flycotablayout)

    implementation(libs.lucksiege.ucrop)
    implementation(libs.lucksiege.camerax)
    implementation(libs.lucksiege.compress)
    implementation(libs.pictureselector)

    implementation(libs.androidx.media3.transformer)
    implementation(libs.androidx.media3.common)

    // Testing
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}